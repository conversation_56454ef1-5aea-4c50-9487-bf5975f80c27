package com.security.checker.resolver;

import com.security.checker.resolver.MavenDependencyResolver.DependencyInfo;
import com.security.checker.resolver.MavenDependencyResolver.DependencyConflict;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.io.TempDir;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.file.Path;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Maven依赖解析器单元测试
 */
@SpringBootTest
@ActiveProfiles("test")
public class MavenDependencyResolverTest {

    private MavenDependencyResolver resolver;
    
    @TempDir
    Path tempDir;
    
    @BeforeEach
    void setUp() {
        resolver = new MavenDependencyResolver();
    }
    
    @Test
    @DisplayName("应该能够解析简单的POM文件")
    void shouldParseSimplePomFile() throws Exception {
        // Given
        File pomFile = createTestPomFile("simple-pom.xml", """
            <?xml version="1.0" encoding="UTF-8"?>
            <project xmlns="http://maven.apache.org/POM/4.0.0"
                     xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                     xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
                     http://maven.apache.org/xsd/maven-4.0.0.xsd">
                <modelVersion>4.0.0</modelVersion>
                
                <groupId>com.example</groupId>
                <artifactId>test-project</artifactId>
                <version>1.0.0</version>
                <packaging>jar</packaging>
                
                <dependencies>
                    <dependency>
                        <groupId>org.apache.commons</groupId>
                        <artifactId>commons-lang3</artifactId>
                        <version>3.12.0</version>
                    </dependency>
                    <dependency>
                        <groupId>mysql</groupId>
                        <artifactId>mysql-connector-java</artifactId>
                        <version>8.0.28</version>
                    </dependency>
                </dependencies>
            </project>
            """);
        
        // When
        List<DependencyInfo> dependencies = resolver.resolveDependencies(pomFile, false);
        
        // Then
        assertNotNull(dependencies, "依赖列表不应为空");
        assertEquals(2, dependencies.size(), "应该解析出2个依赖");
        
        DependencyInfo commonsLang = findDependency(dependencies, "org.apache.commons", "commons-lang3");
        assertNotNull(commonsLang, "应该找到commons-lang3依赖");
        assertEquals("3.12.0", commonsLang.getVersion(), "版本应该正确");
        assertEquals("compile", commonsLang.getScope(), "默认scope应该是compile");
        assertTrue(commonsLang.isDirect(), "应该是直接依赖");
        assertFalse(commonsLang.getLicenses().isEmpty(), "应该有许可证信息");
        assertTrue(commonsLang.getLicenses().contains("Apache-2.0"), "应该包含Apache-2.0许可证");
        
        DependencyInfo mysqlConnector = findDependency(dependencies, "mysql", "mysql-connector-java");
        assertNotNull(mysqlConnector, "应该找到mysql-connector-java依赖");
        assertEquals("8.0.28", mysqlConnector.getVersion(), "版本应该正确");
        assertTrue(mysqlConnector.getLicenses().contains("GPL-2.0"), "应该包含GPL-2.0许可证");
    }
    
    @Test
    @DisplayName("应该能够跳过test scope的依赖")
    void shouldSkipTestScopeDependencies() throws Exception {
        // Given
        File pomFile = createTestPomFile("test-scope-pom.xml", """
            <?xml version="1.0" encoding="UTF-8"?>
            <project xmlns="http://maven.apache.org/POM/4.0.0"
                     xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                     xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
                     http://maven.apache.org/xsd/maven-4.0.0.xsd">
                <modelVersion>4.0.0</modelVersion>
                
                <groupId>com.example</groupId>
                <artifactId>test-project</artifactId>
                <version>1.0.0</version>
                
                <dependencies>
                    <dependency>
                        <groupId>org.apache.commons</groupId>
                        <artifactId>commons-lang3</artifactId>
                        <version>3.12.0</version>
                        <scope>compile</scope>
                    </dependency>
                    <dependency>
                        <groupId>junit</groupId>
                        <artifactId>junit</artifactId>
                        <version>4.13.2</version>
                        <scope>test</scope>
                    </dependency>
                </dependencies>
            </project>
            """);
        
        // When
        List<DependencyInfo> dependencies = resolver.resolveDependencies(pomFile, false);
        
        // Then
        assertNotNull(dependencies, "依赖列表不应为空");
        assertEquals(1, dependencies.size(), "应该只解析出1个依赖（跳过test scope）");
        
        DependencyInfo commonsLang = findDependency(dependencies, "org.apache.commons", "commons-lang3");
        assertNotNull(commonsLang, "应该找到commons-lang3依赖");
        
        DependencyInfo junit = findDependency(dependencies, "junit", "junit");
        assertNull(junit, "不应该找到junit依赖（test scope）");
    }
    
    @Test
    @DisplayName("应该能够处理属性引用的版本号")
    void shouldHandlePropertyVersions() throws Exception {
        // Given
        File pomFile = createTestPomFile("property-version-pom.xml", """
            <?xml version="1.0" encoding="UTF-8"?>
            <project xmlns="http://maven.apache.org/POM/4.0.0"
                     xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                     xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
                     http://maven.apache.org/xsd/maven-4.0.0.xsd">
                <modelVersion>4.0.0</modelVersion>
                
                <groupId>com.example</groupId>
                <artifactId>test-project</artifactId>
                <version>1.0.0</version>
                
                <properties>
                    <commons-lang3.version>3.12.0</commons-lang3.version>
                </properties>
                
                <dependencies>
                    <dependency>
                        <groupId>org.apache.commons</groupId>
                        <artifactId>commons-lang3</artifactId>
                        <version>${commons-lang3.version}</version>
                    </dependency>
                </dependencies>
            </project>
            """);
        
        // When
        List<DependencyInfo> dependencies = resolver.resolveDependencies(pomFile, false);
        
        // Then
        assertNotNull(dependencies, "依赖列表不应为空");
        assertEquals(1, dependencies.size(), "应该解析出1个依赖");
        
        DependencyInfo commonsLang = findDependency(dependencies, "org.apache.commons", "commons-lang3");
        assertNotNull(commonsLang, "应该找到commons-lang3依赖");
        assertEquals("3.12.0", commonsLang.getVersion(), "属性引用的版本应该被正确解析");
    }
    
    @Test
    @DisplayName("应该能够检测依赖冲突")
    void shouldDetectDependencyConflicts() {
        // Given
        DependencyInfo dep1 = createDependency("org.apache.commons", "commons-lang3", "3.11.0");
        DependencyInfo dep2 = createDependency("org.apache.commons", "commons-lang3", "3.12.0");
        DependencyInfo dep3 = createDependency("mysql", "mysql-connector-java", "8.0.28");
        
        List<DependencyInfo> dependencies = List.of(dep1, dep2, dep3);
        
        // When
        List<DependencyConflict> conflicts = resolver.checkDependencyConflicts(dependencies);
        
        // Then
        assertNotNull(conflicts, "冲突列表不应为空");
        assertEquals(1, conflicts.size(), "应该检测到1个冲突");
        
        DependencyConflict conflict = conflicts.get(0);
        assertEquals("org.apache.commons", conflict.getGroupId(), "冲突的groupId应该正确");
        assertEquals("commons-lang3", conflict.getArtifactId(), "冲突的artifactId应该正确");
        assertEquals(2, conflict.getConflictingVersions().size(), "应该有2个冲突版本");
        assertTrue(conflict.getConflictingVersions().contains("3.11.0"), "应该包含版本3.11.0");
        assertTrue(conflict.getConflictingVersions().contains("3.12.0"), "应该包含版本3.12.0");
    }
    
    @Test
    @DisplayName("应该能够处理无效的POM文件")
    void shouldHandleInvalidPomFile() throws IOException {
        // Given
        File invalidPomFile = createTestPomFile("invalid-pom.xml", """
            <?xml version="1.0" encoding="UTF-8"?>
            <invalid-xml>
                <this-is-not-a-valid-pom>
            """);
        
        // When & Then
        assertThrows(Exception.class, () -> {
            resolver.resolveDependencies(invalidPomFile, false);
        }, "无效的POM文件应该抛出异常");
    }
    
    @Test
    @DisplayName("应该能够处理不存在的POM文件")
    void shouldHandleNonExistentPomFile() {
        // Given
        File nonExistentFile = new File(tempDir.toFile(), "non-existent.xml");
        
        // When & Then
        assertThrows(Exception.class, () -> {
            resolver.resolveDependencies(nonExistentFile, false);
        }, "不存在的POM文件应该抛出异常");
    }
    
    @Test
    @DisplayName("应该能够去重和排序依赖")
    void shouldDeduplicateAndSortDependencies() throws Exception {
        // Given
        File pomFile = createTestPomFile("duplicate-pom.xml", """
            <?xml version="1.0" encoding="UTF-8"?>
            <project xmlns="http://maven.apache.org/POM/4.0.0"
                     xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                     xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
                     http://maven.apache.org/xsd/maven-4.0.0.xsd">
                <modelVersion>4.0.0</modelVersion>
                
                <groupId>com.example</groupId>
                <artifactId>test-project</artifactId>
                <version>1.0.0</version>
                
                <dependencies>
                    <dependency>
                        <groupId>mysql</groupId>
                        <artifactId>mysql-connector-java</artifactId>
                        <version>8.0.28</version>
                    </dependency>
                    <dependency>
                        <groupId>org.apache.commons</groupId>
                        <artifactId>commons-lang3</artifactId>
                        <version>3.12.0</version>
                    </dependency>
                </dependencies>
            </project>
            """);
        
        // When
        List<DependencyInfo> dependencies = resolver.resolveDependencies(pomFile, false);
        
        // Then
        assertNotNull(dependencies, "依赖列表不应为空");
        assertEquals(2, dependencies.size(), "应该有2个依赖");
        
        // 验证排序（按groupId:artifactId排序）
        assertEquals("mysql:mysql-connector-java", dependencies.get(0).getCoordinate().substring(0, 
                dependencies.get(0).getCoordinate().lastIndexOf(':')), "第一个应该是mysql");
        assertEquals("org.apache.commons:commons-lang3", dependencies.get(1).getCoordinate().substring(0, 
                dependencies.get(1).getCoordinate().lastIndexOf(':')), "第二个应该是commons-lang3");
    }
    
    // 辅助方法
    private File createTestPomFile(String fileName, String content) throws IOException {
        File pomFile = tempDir.resolve(fileName).toFile();
        try (FileWriter writer = new FileWriter(pomFile)) {
            writer.write(content);
        }
        return pomFile;
    }
    
    private DependencyInfo findDependency(List<DependencyInfo> dependencies, String groupId, String artifactId) {
        return dependencies.stream()
                .filter(dep -> groupId.equals(dep.getGroupId()) && artifactId.equals(dep.getArtifactId()))
                .findFirst()
                .orElse(null);
    }
    
    private DependencyInfo createDependency(String groupId, String artifactId, String version) {
        DependencyInfo dependency = new DependencyInfo();
        dependency.setGroupId(groupId);
        dependency.setArtifactId(artifactId);
        dependency.setVersion(version);
        dependency.setScope("compile");
        dependency.setType("jar");
        dependency.setDirect(true);
        return dependency;
    }
}
