package com.security.checker;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import static org.junit.jupiter.api.Assertions.*;

/**
 * Unit test for Maven Dependency Security Checker App.
 */
public class AppTest {

    @Test
    @DisplayName("应用程序基本测试")
    public void testApp() {
        assertTrue(true, "基本测试应该通过");
    }

    @Test
    @DisplayName("应用程序主类存在测试")
    public void testMainClassExists() {
        assertNotNull(App.class, "主应用类应该存在");
    }
}
