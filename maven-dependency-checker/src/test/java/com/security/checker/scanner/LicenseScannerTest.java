package com.security.checker.scanner;

import com.security.checker.resolver.MavenDependencyResolver.DependencyInfo;
import com.security.checker.repository.LicenseRepository;
import com.security.checker.repository.ComponentLicenseRepository;
import com.security.checker.entity.LicenseEntity;
import com.security.checker.scanner.LicenseScanner.LicenseScanResult;
import com.security.checker.scanner.LicenseScanner.LicenseInfo;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * 许可证扫描器单元测试
 */
@SpringBootTest
@ActiveProfiles("test")
public class LicenseScannerTest {

    private LicenseScanner licenseScanner;
    
    @Mock
    private LicenseRepository licenseRepository;
    
    @Mock
    private ComponentLicenseRepository componentLicenseRepository;
    
    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        licenseScanner = new LicenseScanner();
        
        // 注入模拟的依赖
        ReflectionTestUtils.setField(licenseScanner, "licenseRepository", licenseRepository);
        ReflectionTestUtils.setField(licenseScanner, "componentLicenseRepository", componentLicenseRepository);
        ReflectionTestUtils.setField(licenseScanner, "enableFuzzyMatching", true);
        ReflectionTestUtils.setField(licenseScanner, "strictMode", false);
    }
    
    @Test
    @DisplayName("应该能够扫描具有Apache许可证的依赖")
    void shouldScanDependencyWithApacheLicense() {
        // Given
        DependencyInfo dependency = createDependency("org.apache.commons", "commons-lang3", "3.12.0");
        dependency.setLicenses(Arrays.asList("Apache-2.0"));
        
        LicenseEntity apacheLicense = createLicenseEntity("Apache-2.0", "Apache License 2.0", false, "LOW");
        when(licenseRepository.findByLicenseId("Apache-2.0")).thenReturn(Optional.of(apacheLicense));
        when(componentLicenseRepository.findByGroupIdAndArtifactId(any(), any())).thenReturn(Arrays.asList());
        
        // When
        LicenseScanResult result = licenseScanner.scanDependencies(Arrays.asList(dependency));
        
        // Then
        assertNotNull(result, "扫描结果不应为空");
        assertEquals(1, result.getTotalDependencies(), "应该扫描1个依赖");
        assertEquals(0, result.getRestrictedLicenseCount(), "Apache许可证不应被标记为限制性");
        assertEquals(1, result.getCompatibleLicenseCount(), "Apache许可证应被标记为兼容");
        assertEquals(0, result.getUnknownLicenseCount(), "不应有未知许可证");
        
        List<LicenseInfo> licenseInfos = result.getAllLicenseInfos();
        assertEquals(1, licenseInfos.size(), "应该有1个许可证信息");
        
        LicenseInfo licenseInfo = licenseInfos.get(0);
        assertEquals("Apache License 2.0", licenseInfo.getLicenseName(), "许可证名称应该正确");
        assertFalse(licenseInfo.isRestricted(), "Apache许可证不应被限制");
        assertEquals("LOW", licenseInfo.getRiskLevel(), "风险等级应该是LOW");
    }
    
    @Test
    @DisplayName("应该能够扫描具有GPL许可证的依赖")
    void shouldScanDependencyWithGPLLicense() {
        // Given
        DependencyInfo dependency = createDependency("mysql", "mysql-connector-java", "8.0.28");
        dependency.setLicenses(Arrays.asList("GPL-2.0"));
        
        LicenseEntity gplLicense = createLicenseEntity("GPL-2.0", "GNU General Public License v2.0", true, "HIGH");
        when(licenseRepository.findByLicenseId("GPL-2.0")).thenReturn(Optional.of(gplLicense));
        when(componentLicenseRepository.findByGroupIdAndArtifactId(any(), any())).thenReturn(Arrays.asList());
        
        // When
        LicenseScanResult result = licenseScanner.scanDependencies(Arrays.asList(dependency));
        
        // Then
        assertNotNull(result, "扫描结果不应为空");
        assertEquals(1, result.getTotalDependencies(), "应该扫描1个依赖");
        assertEquals(1, result.getRestrictedLicenseCount(), "GPL许可证应被标记为限制性");
        assertEquals(0, result.getCompatibleLicenseCount(), "GPL许可证不应被标记为兼容");
        assertEquals(0, result.getUnknownLicenseCount(), "不应有未知许可证");
        
        List<LicenseInfo> licenseInfos = result.getAllLicenseInfos();
        assertEquals(1, licenseInfos.size(), "应该有1个许可证信息");
        
        LicenseInfo licenseInfo = licenseInfos.get(0);
        assertEquals("GNU General Public License v2.0", licenseInfo.getLicenseName(), "许可证名称应该正确");
        assertTrue(licenseInfo.isRestricted(), "GPL许可证应被限制");
        assertEquals("HIGH", licenseInfo.getRiskLevel(), "风险等级应该是HIGH");
    }
    
    @Test
    @DisplayName("应该能够处理没有许可证信息的依赖")
    void shouldHandleDependencyWithoutLicense() {
        // Given
        DependencyInfo dependency = createDependency("com.example", "unknown-lib", "1.0.0");
        dependency.setLicenses(Arrays.asList()); // 没有许可证信息
        
        when(componentLicenseRepository.findByGroupIdAndArtifactId(any(), any())).thenReturn(Arrays.asList());
        
        // When
        LicenseScanResult result = licenseScanner.scanDependencies(Arrays.asList(dependency));
        
        // Then
        assertNotNull(result, "扫描结果不应为空");
        assertEquals(1, result.getTotalDependencies(), "应该扫描1个依赖");
        assertEquals(0, result.getRestrictedLicenseCount(), "不应有限制性许可证");
        assertEquals(0, result.getCompatibleLicenseCount(), "不应有兼容许可证");
        assertEquals(1, result.getUnknownLicenseCount(), "应该有1个未知许可证");
    }
    
    @Test
    @DisplayName("应该能够扫描多个依赖")
    void shouldScanMultipleDependencies() {
        // Given
        DependencyInfo apacheDep = createDependency("org.apache.commons", "commons-lang3", "3.12.0");
        apacheDep.setLicenses(Arrays.asList("Apache-2.0"));
        
        DependencyInfo gplDep = createDependency("mysql", "mysql-connector-java", "8.0.28");
        gplDep.setLicenses(Arrays.asList("GPL-2.0"));
        
        DependencyInfo unknownDep = createDependency("com.example", "unknown-lib", "1.0.0");
        unknownDep.setLicenses(Arrays.asList());
        
        LicenseEntity apacheLicense = createLicenseEntity("Apache-2.0", "Apache License 2.0", false, "LOW");
        LicenseEntity gplLicense = createLicenseEntity("GPL-2.0", "GNU General Public License v2.0", true, "HIGH");
        
        when(licenseRepository.findByLicenseId("Apache-2.0")).thenReturn(Optional.of(apacheLicense));
        when(licenseRepository.findByLicenseId("GPL-2.0")).thenReturn(Optional.of(gplLicense));
        when(componentLicenseRepository.findByGroupIdAndArtifactId(any(), any())).thenReturn(Arrays.asList());
        
        // When
        LicenseScanResult result = licenseScanner.scanDependencies(Arrays.asList(apacheDep, gplDep, unknownDep));
        
        // Then
        assertNotNull(result, "扫描结果不应为空");
        assertEquals(3, result.getTotalDependencies(), "应该扫描3个依赖");
        assertEquals(1, result.getRestrictedLicenseCount(), "应该有1个限制性许可证");
        assertEquals(1, result.getCompatibleLicenseCount(), "应该有1个兼容许可证");
        assertEquals(1, result.getUnknownLicenseCount(), "应该有1个未知许可证");
        
        List<LicenseInfo> licenseInfos = result.getAllLicenseInfos();
        assertEquals(2, licenseInfos.size(), "应该有2个许可证信息（不包括未知的）");
    }
    
    @Test
    @DisplayName("应该能够处理空的依赖列表")
    void shouldHandleEmptyDependencyList() {
        // When
        LicenseScanResult result = licenseScanner.scanDependencies(Arrays.asList());
        
        // Then
        assertNotNull(result, "扫描结果不应为空");
        assertEquals(0, result.getTotalDependencies(), "应该扫描0个依赖");
        assertEquals(0, result.getRestrictedLicenseCount(), "不应有限制性许可证");
        assertEquals(0, result.getCompatibleLicenseCount(), "不应有兼容许可证");
        assertEquals(0, result.getUnknownLicenseCount(), "不应有未知许可证");
    }
    
    @Test
    @DisplayName("应该能够处理null依赖列表")
    void shouldHandleNullDependencyList() {
        // When & Then
        assertThrows(NullPointerException.class, () -> {
            licenseScanner.scanDependencies(null);
        }, "null依赖列表应该抛出异常");
    }
    
    // 辅助方法
    private DependencyInfo createDependency(String groupId, String artifactId, String version) {
        DependencyInfo dependency = new DependencyInfo();
        dependency.setGroupId(groupId);
        dependency.setArtifactId(artifactId);
        dependency.setVersion(version);
        dependency.setScope("compile");
        dependency.setType("jar");
        dependency.setDirect(true);
        return dependency;
    }
    
    private LicenseEntity createLicenseEntity(String licenseId, String name, boolean restricted, String riskLevel) {
        LicenseEntity license = new LicenseEntity();
        license.setLicenseId(licenseId);
        license.setName(name);
        license.setRestricted(restricted);
        license.setRiskLevel(riskLevel);
        license.setCommercialFriendly(!restricted);
        return license;
    }
}
