package com.security.checker.scanner;

import com.security.checker.entity.CveEntity;
import com.security.checker.entity.CveAffectedComponentEntity;
import com.security.checker.repository.CveRepository;
import com.security.checker.resolver.MavenDependencyResolver.DependencyInfo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * CVE扫描器测试
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class CVEScannerTest {

    @Autowired
    private CVEScanner cveScanner;

    @Autowired
    private CveRepository cveRepository;

    @BeforeEach
    void setUp() {
        // 清理测试数据
        cveRepository.deleteAll();
        
        // 创建测试CVE数据
        createTestCveData();
    }

    @Test
    void testScanWithVulnerableDependency() {
        // 创建一个受影响的依赖
        DependencyInfo dependency = new DependencyInfo();
        dependency.setGroupId("org.springframework");
        dependency.setArtifactId("spring-webmvc");
        dependency.setVersion("5.3.10");
        dependency.setScope("compile");

        List<DependencyInfo> dependencies = Arrays.asList(dependency);

        // 执行扫描
        CVEScanner.CVEScanResult result = cveScanner.scan(dependencies);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.getTotalVulnerabilities());
        assertEquals(1, result.getCriticalCount());
        assertEquals(0, result.getHighCount());
        assertEquals(0, result.getMediumCount());
        assertEquals(0, result.getLowCount());
        assertTrue(result.hasHighSeverityVulnerabilities());

        // 验证漏洞详情
        List<VulnerabilityInfo> vulnerabilities = result.getVulnerabilities();
        assertEquals(1, vulnerabilities.size());
        
        VulnerabilityInfo vuln = vulnerabilities.get(0);
        assertEquals("CVE-TEST-22965", vuln.getCveId());
        assertEquals("CRITICAL", vuln.getSeverity());
        assertEquals(9.8, vuln.getCvssScore());
        assertEquals("org.springframework:spring-webmvc:5.3.10", vuln.getAffectedDependency());
    }

    @Test
    void testScanWithNonVulnerableDependency() {
        // 创建一个不受影响的依赖（版本太新）
        DependencyInfo dependency = new DependencyInfo();
        dependency.setGroupId("org.springframework");
        dependency.setArtifactId("spring-webmvc");
        dependency.setVersion("5.3.20");
        dependency.setScope("compile");

        List<DependencyInfo> dependencies = Arrays.asList(dependency);

        // 执行扫描
        CVEScanner.CVEScanResult result = cveScanner.scan(dependencies);

        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.getTotalVulnerabilities());
        assertFalse(result.hasHighSeverityVulnerabilities());
    }

    @Test
    void testScanWithUnknownDependency() {
        // 创建一个未知的依赖
        DependencyInfo dependency = new DependencyInfo();
        dependency.setGroupId("com.unknown");
        dependency.setArtifactId("unknown-library");
        dependency.setVersion("1.0.0");
        dependency.setScope("compile");

        List<DependencyInfo> dependencies = Arrays.asList(dependency);

        // 执行扫描
        CVEScanner.CVEScanResult result = cveScanner.scan(dependencies);

        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.getTotalVulnerabilities());
    }

    @Test
    void testScanWithMultipleDependencies() {
        // 创建多个依赖，包括受影响和不受影响的
        DependencyInfo vuln1 = new DependencyInfo();
        vuln1.setGroupId("org.springframework");
        vuln1.setArtifactId("spring-webmvc");
        vuln1.setVersion("5.3.10");
        vuln1.setScope("compile");

        DependencyInfo vuln2 = new DependencyInfo();
        vuln2.setGroupId("com.fasterxml.jackson.core");
        vuln2.setArtifactId("jackson-databind");
        vuln2.setVersion("2.12.0");
        vuln2.setScope("compile");

        DependencyInfo safe = new DependencyInfo();
        safe.setGroupId("org.springframework");
        safe.setArtifactId("spring-webmvc");
        safe.setVersion("5.3.20");
        safe.setScope("compile");

        List<DependencyInfo> dependencies = Arrays.asList(vuln1, vuln2, safe);

        // 执行扫描
        CVEScanner.CVEScanResult result = cveScanner.scan(dependencies);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.getTotalVulnerabilities());
        assertEquals(1, result.getCriticalCount());
        assertEquals(1, result.getHighCount());
        assertTrue(result.hasHighSeverityVulnerabilities());
    }

    private void createTestCveData() {
        // 创建Spring Framework CVE (使用测试专用的CVE ID)
        CveEntity springCve = new CveEntity();
        springCve.setCveId("CVE-TEST-22965");
        springCve.setDescription("Spring Framework RCE via Data Binding on JDK 9+ (Test)");
        springCve.setSeverity("CRITICAL");
        springCve.setCvssScore(9.8);
        springCve.setPublishedDate(LocalDateTime.of(2022, 3, 31, 0, 0));
        springCve.setLastModifiedDate(LocalDateTime.of(2022, 4, 1, 0, 0));
        springCve.setStatus("ANALYZED");
        springCve.setReferences("[{\"url\":\"https://spring.io/security/cve-2022-22965\"}]");

        CveAffectedComponentEntity springComponent = new CveAffectedComponentEntity();
        springComponent.setCve(springCve);
        springComponent.setVendor("pivotal");
        springComponent.setProduct("spring-webmvc");
        springComponent.setVersionStartIncluding("5.3.0");
        springComponent.setVersionEndExcluding("5.3.18");
        springComponent.setVulnerable(true);

        springCve.addAffectedComponent(springComponent);
        cveRepository.save(springCve);

        // 创建Jackson CVE (使用测试专用的CVE ID)
        CveEntity jacksonCve = new CveEntity();
        jacksonCve.setCveId("CVE-TEST-36518");
        jacksonCve.setDescription("Jackson Databind before 2.13.0 allows a Java StackOverflow exception (Test)");
        jacksonCve.setSeverity("HIGH");
        jacksonCve.setCvssScore(7.5);
        jacksonCve.setPublishedDate(LocalDateTime.of(2022, 3, 11, 0, 0));
        jacksonCve.setLastModifiedDate(LocalDateTime.of(2022, 3, 17, 0, 0));
        jacksonCve.setStatus("ANALYZED");
        jacksonCve.setReferences("[{\"url\":\"https://github.com/FasterXML/jackson-databind/issues/2816\"}]");

        CveAffectedComponentEntity jacksonComponent = new CveAffectedComponentEntity();
        jacksonComponent.setCve(jacksonCve);
        jacksonComponent.setVendor("fasterxml");
        jacksonComponent.setProduct("jackson-databind");
        jacksonComponent.setVersionEndExcluding("2.13.0");
        jacksonComponent.setVulnerable(true);

        jacksonCve.addAffectedComponent(jacksonComponent);
        cveRepository.save(jacksonCve);
    }
}
