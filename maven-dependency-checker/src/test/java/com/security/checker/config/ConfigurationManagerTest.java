package com.security.checker.config;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.io.TempDir;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.file.Path;
import java.nio.file.Files;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 配置管理器单元测试
 */
@SpringBootTest
@ActiveProfiles("test")
public class ConfigurationManagerTest {

    private ConfigurationManager configurationManager;
    
    @TempDir
    Path tempDir;
    
    @BeforeEach
    void setUp() {
        configurationManager = new ConfigurationManager();
    }
    
    @AfterEach
    void tearDown() {
        // 清理测试环境
    }
    
    @Test
    @DisplayName("应该能够生成默认配置内容")
    void shouldGenerateDefaultConfigContent() {
        // When
        String defaultConfig = configurationManager.generateDefaultConfigContent();
        
        // Then
        assertNotNull(defaultConfig, "默认配置内容不应为空");
        assertTrue(defaultConfig.contains("security-checker:"), "应包含主配置节点");
        assertTrue(defaultConfig.contains("cve:"), "应包含CVE配置节点");
        assertTrue(defaultConfig.contains("license:"), "应包含许可证配置节点");
        assertTrue(defaultConfig.contains("report:"), "应包含报告配置节点");
        assertTrue(defaultConfig.contains("maven:"), "应包含Maven配置节点");
    }
    
    @Test
    @DisplayName("应该能够获取用户配置目录")
    void shouldGetUserConfigDirectory() {
        // When
        File userConfigDir = configurationManager.getUserConfigDirectory();
        
        // Then
        assertNotNull(userConfigDir, "用户配置目录不应为空");
        assertTrue(userConfigDir.getPath().contains(".maven-checker"), 
                  "用户配置目录应包含.maven-checker");
    }
    
    @Test
    @DisplayName("应该能够创建用户配置目录")
    void shouldCreateUserConfigDirectory() {
        // Given
        File userConfigDir = configurationManager.getUserConfigDirectory();
        
        // When
        boolean created = configurationManager.ensureUserConfigDirectory();
        
        // Then
        assertTrue(created || userConfigDir.exists(), "用户配置目录应该被创建或已存在");
        assertTrue(userConfigDir.exists(), "用户配置目录应该存在");
        assertTrue(userConfigDir.isDirectory(), "应该是一个目录");
    }
    
    @Test
    @DisplayName("应该能够生成默认配置文件")
    void shouldGenerateDefaultConfigFile() throws IOException {
        // Given
        File configFile = tempDir.resolve("test-config.yml").toFile();
        
        // When
        boolean generated = configurationManager.generateDefaultConfigFile(configFile.getPath());
        
        // Then
        assertTrue(generated, "应该成功生成配置文件");
        assertTrue(configFile.exists(), "配置文件应该存在");
        
        String content = Files.readString(configFile.toPath());
        assertTrue(content.contains("security-checker:"), "配置文件应包含主配置节点");
    }
    
    @Test
    @DisplayName("应该能够验证有效的配置文件")
    void shouldValidateValidConfigFile() throws IOException {
        // Given
        File configFile = tempDir.resolve("valid-config.yml").toFile();
        String validConfig = """
            security-checker:
              cve:
                enable: true
                severity-threshold: MEDIUM
              license:
                enable: true
                strict-mode: false
              report:
                format: HTML
                output-path: ./reports
              maven:
                local-repository: ~/.m2/repository
            """;
        
        try (FileWriter writer = new FileWriter(configFile)) {
            writer.write(validConfig);
        }
        
        // When
        boolean isValid = configurationManager.validateConfigFile(configFile.getPath());
        
        // Then
        assertTrue(isValid, "有效的配置文件应该通过验证");
    }
    
    @Test
    @DisplayName("应该能够检测无效的配置文件")
    void shouldDetectInvalidConfigFile() throws IOException {
        // Given
        File configFile = tempDir.resolve("invalid-config.yml").toFile();
        String invalidConfig = """
            invalid-yaml-content:
              - this is not
                - properly formatted
            """;
        
        try (FileWriter writer = new FileWriter(configFile)) {
            writer.write(invalidConfig);
        }
        
        // When
        boolean isValid = configurationManager.validateConfigFile(configFile.getPath());
        
        // Then
        assertFalse(isValid, "无效的配置文件应该验证失败");
    }
    
    @Test
    @DisplayName("应该能够处理不存在的配置文件")
    void shouldHandleNonExistentConfigFile() {
        // Given
        String nonExistentFile = tempDir.resolve("non-existent.yml").toString();
        
        // When & Then
        assertFalse(configurationManager.validateConfigFile(nonExistentFile), 
                   "不存在的配置文件应该验证失败");
    }
    
    @Test
    @DisplayName("应该能够获取配置信息")
    void shouldGetConfigurationInfo() {
        // When
        String configInfo = configurationManager.getConfigurationInfo();
        
        // Then
        assertNotNull(configInfo, "配置信息不应为空");
        assertTrue(configInfo.contains("用户配置目录"), "应包含用户配置目录信息");
        assertTrue(configInfo.contains("数据库路径"), "应包含数据库路径信息");
    }
    
    @Test
    @DisplayName("应该能够处理配置文件路径为空的情况")
    void shouldHandleNullConfigFilePath() {
        // When & Then
        assertFalse(configurationManager.validateConfigFile(null), 
                   "空配置文件路径应该验证失败");
        assertFalse(configurationManager.generateDefaultConfigFile(null), 
                   "空路径应该生成失败");
    }
    
    @Test
    @DisplayName("应该能够处理配置文件路径为空字符串的情况")
    void shouldHandleEmptyConfigFilePath() {
        // When & Then
        assertFalse(configurationManager.validateConfigFile(""), 
                   "空字符串配置文件路径应该验证失败");
        assertFalse(configurationManager.generateDefaultConfigFile(""), 
                   "空字符串路径应该生成失败");
    }
}
