package com.security.checker.config;

import com.security.checker.entity.DataSyncStatusEntity;
import com.security.checker.entity.LicenseEntity;
import com.security.checker.repository.DataSyncStatusRepository;
import com.security.checker.repository.LicenseRepository;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 数据库配置测试
 */
@SpringBootTest
@ActiveProfiles("test")
public class DatabaseConfigTest {
    
    @Autowired
    private LicenseRepository licenseRepository;
    
    @Autowired
    private DataSyncStatusRepository dataSyncStatusRepository;
    
    @Test
    public void testLicenseInitialization() {
        // 验证许可证数据已初始化
        List<LicenseEntity> licenses = licenseRepository.findAll();
        assertFalse(licenses.isEmpty(), "许可证数据应该已初始化");
        assertTrue(licenses.size() >= 20, "应该有至少20个许可证");
        
        // 验证特定许可证
        Optional<LicenseEntity> apacheLicense = licenseRepository.findByLicenseId("Apache-2.0");
        assertTrue(apacheLicense.isPresent(), "Apache-2.0许可证应该存在");
        assertTrue(apacheLicense.get().getCommercialFriendly(), "Apache-2.0应该是商业友好的");
        assertEquals("NONE", apacheLicense.get().getCopyleftType(), "Apache-2.0应该是非Copyleft");
        
        Optional<LicenseEntity> gplLicense = licenseRepository.findByLicenseId("GPL-3.0");
        assertTrue(gplLicense.isPresent(), "GPL-3.0许可证应该存在");
        assertFalse(gplLicense.get().getCommercialFriendly(), "GPL-3.0应该不是商业友好的");
        assertEquals("STRONG", gplLicense.get().getCopyleftType(), "GPL-3.0应该是强Copyleft");
        
        Optional<LicenseEntity> agplLicense = licenseRepository.findByLicenseId("AGPL-3.0");
        assertTrue(agplLicense.isPresent(), "AGPL-3.0许可证应该存在");
        assertFalse(agplLicense.get().getCommercialFriendly(), "AGPL-3.0应该不是商业友好的");
        assertEquals("NETWORK", agplLicense.get().getCopyleftType(), "AGPL-3.0应该是网络Copyleft");
    }
    
    @Test
    public void testCommercialFriendlyLicenses() {
        List<LicenseEntity> commercialFriendly = licenseRepository.findByCommercialFriendly(true);
        assertFalse(commercialFriendly.isEmpty(), "应该有商业友好的许可证");
        
        // 验证所有商业友好许可证的属性
        for (LicenseEntity license : commercialFriendly) {
            assertTrue(license.getCommercialFriendly(), 
                      "许可证 " + license.getLicenseId() + " 应该是商业友好的");
            assertNotEquals("STRONG", license.getCopyleftType(), 
                           "商业友好许可证不应该是强Copyleft");
            assertNotEquals("NETWORK", license.getCopyleftType(), 
                           "商业友好许可证不应该是网络Copyleft");
        }
    }
    
    @Test
    public void testCopyleftLicenses() {
        List<LicenseEntity> strongCopyleft = licenseRepository.findByCopyleftType("STRONG");
        assertFalse(strongCopyleft.isEmpty(), "应该有强Copyleft许可证");
        
        List<LicenseEntity> networkCopyleft = licenseRepository.findByCopyleftType("NETWORK");
        assertFalse(networkCopyleft.isEmpty(), "应该有网络Copyleft许可证");
        
        // 验证强Copyleft许可证不是商业友好的
        for (LicenseEntity license : strongCopyleft) {
            assertFalse(license.getCommercialFriendly(), 
                       "强Copyleft许可证 " + license.getLicenseId() + " 不应该是商业友好的");
        }
        
        // 验证网络Copyleft许可证不是商业友好的
        for (LicenseEntity license : networkCopyleft) {
            assertFalse(license.getCommercialFriendly(), 
                       "网络Copyleft许可证 " + license.getLicenseId() + " 不应该是商业友好的");
        }
    }
    
    @Test
    public void testDataSyncStatusInitialization() {
        // 验证数据同步状态已初始化
        List<DataSyncStatusEntity> syncStatuses = dataSyncStatusRepository.findAll();
        assertFalse(syncStatuses.isEmpty(), "数据同步状态应该已初始化");
        assertEquals(3, syncStatuses.size(), "应该有3个数据同步状态");
        
        // 验证CVE同步状态
        Optional<DataSyncStatusEntity> cveSync = dataSyncStatusRepository
                .findByDataTypeAndDataSource("CVE", "NVD");
        assertTrue(cveSync.isPresent(), "CVE同步状态应该存在");
        assertEquals("NEVER_SYNCED", cveSync.get().getSyncStatus(), "初始状态应该是NEVER_SYNCED");
        assertTrue(cveSync.get().getAutoSyncEnabled(), "应该启用自动同步");
        assertEquals(24, cveSync.get().getSyncIntervalHours(), "CVE同步间隔应该是24小时");
        
        // 验证LICENSE同步状态
        Optional<DataSyncStatusEntity> licenseSync = dataSyncStatusRepository
                .findByDataTypeAndDataSource("LICENSE", "SPDX");
        assertTrue(licenseSync.isPresent(), "LICENSE同步状态应该存在");
        assertEquals(168, licenseSync.get().getSyncIntervalHours(), "LICENSE同步间隔应该是168小时");
        
        // 验证COMPONENT_METADATA同步状态
        Optional<DataSyncStatusEntity> componentSync = dataSyncStatusRepository
                .findByDataTypeAndDataSource("COMPONENT_METADATA", "MAVEN_CENTRAL");
        assertTrue(componentSync.isPresent(), "COMPONENT_METADATA同步状态应该存在");
    }
    
    @Test
    public void testLicenseRiskLevels() {
        // 验证不同风险级别的许可证
        List<LicenseEntity> lowRisk = licenseRepository.findByRiskLevel("LOW");
        List<LicenseEntity> mediumRisk = licenseRepository.findByRiskLevel("MEDIUM");
        List<LicenseEntity> highRisk = licenseRepository.findByRiskLevel("HIGH");
        List<LicenseEntity> criticalRisk = licenseRepository.findByRiskLevel("CRITICAL");
        
        assertFalse(lowRisk.isEmpty(), "应该有低风险许可证");
        assertFalse(mediumRisk.isEmpty(), "应该有中等风险许可证");
        assertFalse(highRisk.isEmpty(), "应该有高风险许可证");
        assertFalse(criticalRisk.isEmpty(), "应该有严重风险许可证");
        
        // 验证风险级别与商业友好性的关系
        for (LicenseEntity license : lowRisk) {
            assertTrue(license.getCommercialFriendly(), 
                      "低风险许可证应该是商业友好的: " + license.getLicenseId());
        }
        
        for (LicenseEntity license : criticalRisk) {
            assertFalse(license.getCommercialFriendly(), 
                       "严重风险许可证不应该是商业友好的: " + license.getLicenseId());
        }
    }
    
    @Test
    public void testLicenseBusinessLogic() {
        // 测试许可证业务逻辑方法
        Optional<LicenseEntity> gpl = licenseRepository.findByLicenseId("GPL-3.0");
        assertTrue(gpl.isPresent());
        assertTrue(gpl.get().isStrongCopyleft(), "GPL-3.0应该是强Copyleft");
        assertEquals("HIGH", gpl.get().getRiskLevel(), "GPL-3.0应该是高风险");

        Optional<LicenseEntity> agpl = licenseRepository.findByLicenseId("AGPL-3.0");
        assertTrue(agpl.isPresent());
        assertTrue(agpl.get().isStrongCopyleft(), "AGPL-3.0应该是强Copyleft");
        assertEquals("CRITICAL", agpl.get().getRiskLevel(), "AGPL-3.0应该是严重风险");

        Optional<LicenseEntity> apache = licenseRepository.findByLicenseId("Apache-2.0");
        assertTrue(apache.isPresent());
        assertFalse(apache.get().isStrongCopyleft(), "Apache-2.0不应该是强Copyleft");
        assertEquals("LOW", apache.get().getRiskLevel(), "Apache-2.0应该是低风险");
    }
    
    @Test
    public void testDataSyncStatusQueries() {
        // 测试数据同步状态查询方法
        List<DataSyncStatusEntity> neverSynced = dataSyncStatusRepository.findNeverSynced();
        assertEquals(3, neverSynced.size(), "应该有3个从未同步的状态");
        
        List<DataSyncStatusEntity> autoSyncEnabled = dataSyncStatusRepository.findByAutoSyncEnabled(true);
        assertEquals(3, autoSyncEnabled.size(), "应该有3个启用自动同步的状态");
        
        List<DataSyncStatusEntity> activeSyncs = dataSyncStatusRepository.findActiveSyncs();
        assertTrue(activeSyncs.isEmpty(), "初始状态下不应该有正在进行的同步");
        
        List<DataSyncStatusEntity> failedSyncs = dataSyncStatusRepository.findFailedSyncs();
        assertTrue(failedSyncs.isEmpty(), "初始状态下不应该有失败的同步");
    }
}
