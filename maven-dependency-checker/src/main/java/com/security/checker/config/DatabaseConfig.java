package com.security.checker.config;

import com.security.checker.entity.DataSyncStatusEntity;
import com.security.checker.entity.LicenseEntity;
import com.security.checker.repository.DataSyncStatusRepository;
import com.security.checker.repository.LicenseRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

/**
 * 数据库配置和初始化
 */
@Configuration
public class DatabaseConfig {
    
    private static final Logger logger = LoggerFactory.getLogger(DatabaseConfig.class);
    
    @Autowired
    private LicenseRepository licenseRepository;
    
    @Autowired
    private DataSyncStatusRepository dataSyncStatusRepository;
    
    /**
     * 数据库初始化
     */
    @Bean
    public CommandLineRunner initDatabase() {
        return args -> {
            logger.info("开始初始化数据库...");
            
            try {
                initializeLicenses();
                initializeDataSyncStatus();
                logger.info("数据库初始化完成");
            } catch (Exception e) {
                logger.error("数据库初始化失败", e);
            }
        };
    }
    
    /**
     * 初始化常见许可证数据
     */
    @Transactional
    public void initializeLicenses() {
        logger.info("初始化许可证数据...");
        
        // 检查是否已经初始化过
        if (licenseRepository.count() > 0) {
            logger.info("许可证数据已存在，跳过初始化");
            return;
        }
        
        List<LicenseEntity> licenses = Arrays.asList(
            // 商业友好的许可证
            createLicense("Apache-2.0", "Apache License 2.0", true, "NONE", "LOW", 
                         true, true, true, true, false, true),
            createLicense("MIT", "MIT License", true, "NONE", "LOW", 
                         true, true, true, true, false, false),
            createLicense("BSD-3-Clause", "BSD 3-Clause License", true, "NONE", "LOW", 
                         true, true, true, true, false, true),
            createLicense("BSD-2-Clause", "BSD 2-Clause License", true, "NONE", "LOW", 
                         true, true, true, true, false, true),
            createLicense("ISC", "ISC License", true, "NONE", "LOW", 
                         true, true, true, true, false, false),
            
            // 弱Copyleft许可证
            createLicense("LGPL-2.1", "GNU Lesser General Public License v2.1", false, "WEAK", "MEDIUM", 
                         false, true, true, true, true, true),
            createLicense("LGPL-3.0", "GNU Lesser General Public License v3.0", false, "WEAK", "MEDIUM", 
                         false, true, true, true, true, true),
            createLicense("MPL-2.0", "Mozilla Public License 2.0", false, "WEAK", "MEDIUM", 
                         false, true, true, true, true, true),
            createLicense("EPL-1.0", "Eclipse Public License 1.0", false, "WEAK", "MEDIUM", 
                         false, true, true, true, true, true),
            createLicense("EPL-2.0", "Eclipse Public License 2.0", false, "WEAK", "MEDIUM", 
                         false, true, true, true, true, true),
            
            // 强Copyleft许可证
            createLicense("GPL-2.0", "GNU General Public License v2.0", false, "STRONG", "HIGH", 
                         false, true, true, true, true, true),
            createLicense("GPL-3.0", "GNU General Public License v3.0", false, "STRONG", "HIGH", 
                         false, true, true, true, true, true),
            createLicense("AGPL-3.0", "GNU Affero General Public License v3.0", false, "NETWORK", "CRITICAL", 
                         false, true, true, true, true, true),
            
            // 其他商业不友好许可证
            createLicense("SSPL-1.0", "Server Side Public License v1", false, "NETWORK", "CRITICAL", 
                         false, false, true, true, true, true),
            createLicense("BUSL-1.1", "Business Source License 1.1", false, "NONE", "HIGH", 
                         false, false, true, false, false, true),
            createLicense("Commons-Clause", "Commons Clause License", false, "NONE", "HIGH", 
                         false, false, true, true, false, true),
            createLicense("CPAL-1.0", "Common Public Attribution License 1.0", false, "WEAK", "MEDIUM", 
                         false, true, true, true, true, true),
            createLicense("CDDL-1.0", "Common Development and Distribution License 1.0", false, "WEAK", "MEDIUM", 
                         false, true, true, true, true, true),
            createLicense("CDDL-1.1", "Common Development and Distribution License 1.1", false, "WEAK", "MEDIUM", 
                         false, true, true, true, true, true),
            createLicense("OSL-3.0", "Open Software License 3.0", false, "STRONG", "HIGH", 
                         false, true, true, true, true, true),
            createLicense("EUPL-1.2", "European Union Public License 1.2", false, "WEAK", "MEDIUM", 
                         false, true, true, true, true, true)
        );
        
        licenseRepository.saveAll(licenses);
        logger.info("已初始化 {} 个许可证", licenses.size());
    }
    
    /**
     * 创建许可证实体
     */
    private LicenseEntity createLicense(String licenseId, String name, boolean commercialFriendly, 
                                       String copyleftType, String riskLevel, boolean requiresSourceDisclosure,
                                       boolean allowsCommercialUse, boolean allowsModification, 
                                       boolean allowsDistribution, boolean allowsPrivateUse,
                                       boolean requiresCopyrightNotice) {
        LicenseEntity license = new LicenseEntity(licenseId, name);
        license.setSpdxId(licenseId);
        license.setCommercialFriendly(commercialFriendly);
        license.setCopyleftType(copyleftType);
        license.setRiskLevel(riskLevel);
        license.setRequiresSourceDisclosure(requiresSourceDisclosure);
        license.setAllowsCommercialUse(allowsCommercialUse);
        license.setAllowsModification(allowsModification);
        license.setAllowsDistribution(allowsDistribution);
        license.setAllowsPrivateUse(allowsPrivateUse);
        license.setRequiresCopyrightNotice(requiresCopyrightNotice);
        license.setRequiresLicenseInclusion(true);
        license.setRequiresStateChanges(copyleftType.equals("STRONG") || copyleftType.equals("NETWORK"));
        license.setProvidesPatentGrant(licenseId.equals("Apache-2.0") || licenseId.startsWith("GPL"));
        license.setCategory(determineCategory(copyleftType, commercialFriendly));
        license.setDescription(generateDescription(name, commercialFriendly, copyleftType));
        license.setDeprecated(false);
        license.setSource("BUILTIN");
        
        return license;
    }
    
    /**
     * 确定许可证类别
     */
    private String determineCategory(String copyleftType, boolean commercialFriendly) {
        if (!commercialFriendly) {
            return "RESTRICTED";
        }
        
        switch (copyleftType) {
            case "NONE":
                return "PERMISSIVE";
            case "WEAK":
                return "WEAK_COPYLEFT";
            case "STRONG":
            case "NETWORK":
                return "STRONG_COPYLEFT";
            default:
                return "OTHER";
        }
    }
    
    /**
     * 生成许可证描述
     */
    private String generateDescription(String name, boolean commercialFriendly, String copyleftType) {
        StringBuilder desc = new StringBuilder(name);
        
        if (!commercialFriendly) {
            desc.append(" - 商业使用受限");
        } else {
            desc.append(" - 允许商业使用");
        }
        
        switch (copyleftType) {
            case "NONE":
                desc.append("，宽松许可证");
                break;
            case "WEAK":
                desc.append("，弱Copyleft许可证");
                break;
            case "STRONG":
                desc.append("，强Copyleft许可证");
                break;
            case "NETWORK":
                desc.append("，网络Copyleft许可证");
                break;
        }
        
        return desc.toString();
    }
    
    /**
     * 初始化数据同步状态
     */
    @Transactional
    public void initializeDataSyncStatus() {
        logger.info("初始化数据同步状态...");
        
        // 检查是否已经初始化过
        if (dataSyncStatusRepository.count() > 0) {
            logger.info("数据同步状态已存在，跳过初始化");
            return;
        }
        
        List<DataSyncStatusEntity> syncStatuses = Arrays.asList(
            createSyncStatus("CVE", "NVD", 24),
            createSyncStatus("LICENSE", "SPDX", 168), // 一周同步一次
            createSyncStatus("COMPONENT_METADATA", "MAVEN_CENTRAL", 24)
        );
        
        dataSyncStatusRepository.saveAll(syncStatuses);
        logger.info("已初始化 {} 个数据同步状态", syncStatuses.size());
    }
    
    /**
     * 创建数据同步状态实体
     */
    private DataSyncStatusEntity createSyncStatus(String dataType, String dataSource, int intervalHours) {
        DataSyncStatusEntity syncStatus = new DataSyncStatusEntity(dataType, dataSource);
        syncStatus.setSyncStatus("NEVER_SYNCED");
        syncStatus.setSyncIntervalHours(intervalHours);
        syncStatus.setAutoSyncEnabled(true);
        syncStatus.setMaxRetries(3);
        syncStatus.setRetryCount(0);
        syncStatus.setProgressPercentage(0);
        syncStatus.setTotalRecords(0L);
        syncStatus.setNewRecords(0L);
        syncStatus.setUpdatedRecords(0L);
        syncStatus.setDeletedRecords(0L);
        syncStatus.setFailedRecords(0L);
        
        return syncStatus;
    }
}
