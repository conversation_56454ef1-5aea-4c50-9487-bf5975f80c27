package com.security.checker.config;

import com.security.checker.entity.CveEntity;
import com.security.checker.entity.CveAffectedComponentEntity;
import com.security.checker.repository.CveRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

import java.time.LocalDateTime;

/**
 * 测试CVE数据初始化器
 * 用于演示和测试CVE扫描功能
 */
@Configuration
@Profile({"dev", "test"})
public class TestCveDataInitializer {
    
    @Autowired
    private CveRepository cveRepository;
    
    @Bean
    public CommandLineRunner initTestCveData() {
        return args -> {
            if (cveRepository.count() == 0) {
                initializeSampleCveData();
            }
        };
    }
    
    private void initializeSampleCveData() {
        System.out.println("🔧 初始化测试CVE数据...");
        
        // 1. Spring Framework 漏洞
        createSpringFrameworkCve();
        
        // 2. <PERSON> 漏洞
        createJacksonCve();
        
        // 3. Log4j 漏洞
        createLog4jCve();
        
        // 4. Apache Commons 漏洞
        createApacheCommonsCve();
        
        // 5. Hibernate 漏洞
        createHibernateCve();
        
        System.out.println("✅ 已初始化 " + cveRepository.count() + " 个测试CVE数据");
    }
    
    private void createSpringFrameworkCve() {
        CveEntity cve = new CveEntity();
        cve.setCveId("CVE-2022-22965");
        cve.setDescription("Spring Framework RCE via Data Binding on JDK 9+");
        cve.setSeverity("CRITICAL");
        cve.setCvssScore(9.8);
        cve.setPublishedDate(LocalDateTime.of(2022, 3, 31, 0, 0));
        cve.setLastModifiedDate(LocalDateTime.of(2022, 4, 1, 0, 0));
        cve.setStatus("ANALYZED");
        cve.setReferences("[{\"url\":\"https://spring.io/security/cve-2022-22965\"}]");
        
        CveAffectedComponentEntity component = new CveAffectedComponentEntity();
        component.setCve(cve);
        component.setVendor("pivotal");
        component.setProduct("spring-framework");
        component.setVersionStartIncluding("5.3.0");
        component.setVersionEndExcluding("5.3.18");
        component.setVulnerable(true);
        
        cve.addAffectedComponent(component);
        
        // 添加另一个受影响版本范围
        CveAffectedComponentEntity component2 = new CveAffectedComponentEntity();
        component2.setCve(cve);
        component2.setVendor("pivotal");
        component2.setProduct("spring-webmvc");
        component2.setVersionStartIncluding("5.3.0");
        component2.setVersionEndExcluding("5.3.18");
        component2.setVulnerable(true);
        
        cve.addAffectedComponent(component2);
        
        cveRepository.save(cve);
    }
    
    private void createJacksonCve() {
        CveEntity cve = new CveEntity();
        cve.setCveId("CVE-2020-36518");
        cve.setDescription("Jackson Databind before 2.13.0 allows a Java StackOverflow exception");
        cve.setSeverity("HIGH");
        cve.setCvssScore(7.5);
        cve.setPublishedDate(LocalDateTime.of(2022, 3, 11, 0, 0));
        cve.setLastModifiedDate(LocalDateTime.of(2022, 3, 17, 0, 0));
        cve.setStatus("ANALYZED");
        cve.setReferences("[{\"url\":\"https://github.com/FasterXML/jackson-databind/issues/2816\"}]");
        
        CveAffectedComponentEntity component = new CveAffectedComponentEntity();
        component.setCve(cve);
        component.setVendor("fasterxml");
        component.setProduct("jackson-databind");
        component.setVersionEndExcluding("2.13.0");
        component.setVulnerable(true);
        
        cve.addAffectedComponent(component);
        cveRepository.save(cve);
    }
    
    private void createLog4jCve() {
        CveEntity cve = new CveEntity();
        cve.setCveId("CVE-2021-44228");
        cve.setDescription("Apache Log4j2 JNDI features do not protect against attacker controlled LDAP");
        cve.setSeverity("CRITICAL");
        cve.setCvssScore(10.0);
        cve.setPublishedDate(LocalDateTime.of(2021, 12, 10, 0, 0));
        cve.setLastModifiedDate(LocalDateTime.of(2021, 12, 14, 0, 0));
        cve.setStatus("ANALYZED");
        cve.setReferences("[{\"url\":\"https://logging.apache.org/log4j/2.x/security.html\"}]");
        
        CveAffectedComponentEntity component = new CveAffectedComponentEntity();
        component.setCve(cve);
        component.setVendor("apache");
        component.setProduct("log4j-core");
        component.setVersionStartIncluding("2.0");
        component.setVersionEndExcluding("2.15.0");
        component.setVulnerable(true);
        
        cve.addAffectedComponent(component);
        cveRepository.save(cve);
    }
    
    private void createApacheCommonsCve() {
        CveEntity cve = new CveEntity();
        cve.setCveId("CVE-2022-42889");
        cve.setDescription("Apache Commons Text performs variable interpolation, allowing an attacker to access information");
        cve.setSeverity("MEDIUM");
        cve.setCvssScore(6.5);
        cve.setPublishedDate(LocalDateTime.of(2022, 10, 13, 0, 0));
        cve.setLastModifiedDate(LocalDateTime.of(2022, 10, 21, 0, 0));
        cve.setStatus("ANALYZED");
        cve.setReferences("[{\"url\":\"https://commons.apache.org/proper/commons-text/security-reports.html\"}]");
        
        CveAffectedComponentEntity component = new CveAffectedComponentEntity();
        component.setCve(cve);
        component.setVendor("apache");
        component.setProduct("commons-text");
        component.setVersionStartIncluding("1.5");
        component.setVersionEndExcluding("1.10.0");
        component.setVulnerable(true);
        
        cve.addAffectedComponent(component);
        cveRepository.save(cve);
    }
    
    private void createHibernateCve() {
        CveEntity cve = new CveEntity();
        cve.setCveId("CVE-2020-25638");
        cve.setDescription("Hibernate Validator allows attackers to bypass input validation via a special payload");
        cve.setSeverity("MEDIUM");
        cve.setCvssScore(5.3);
        cve.setPublishedDate(LocalDateTime.of(2020, 12, 3, 0, 0));
        cve.setLastModifiedDate(LocalDateTime.of(2020, 12, 9, 0, 0));
        cve.setStatus("ANALYZED");
        cve.setReferences("[{\"url\":\"https://hibernate.atlassian.net/browse/HV-1814\"}]");
        
        CveAffectedComponentEntity component = new CveAffectedComponentEntity();
        component.setCve(cve);
        component.setVendor("redhat");
        component.setProduct("hibernate-validator");
        component.setVersionEndExcluding("6.1.6");
        component.setVulnerable(true);
        
        cve.addAffectedComponent(component);
        cveRepository.save(cve);
    }
}
