package com.security.checker.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.File;
import java.net.MalformedURLException;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;

/**
 * 配置验证器
 * 
 * 功能：
 * 1. 验证配置参数的有效性
 * 2. 检查文件路径和URL的可访问性
 * 3. 提供配置修复建议
 * 4. 生成配置验证报告
 * 
 * <AUTHOR>
 */
@Component
public class ConfigValidator {

    private static final Logger logger = LoggerFactory.getLogger(ConfigValidator.class);

    /**
     * 验证结果
     */
    public static class ValidationResult {
        private boolean valid = true;
        private List<String> errors = new ArrayList<>();
        private List<String> warnings = new ArrayList<>();
        private List<String> suggestions = new ArrayList<>();

        public boolean isValid() { return valid; }
        public void setValid(boolean valid) { this.valid = valid; }

        public List<String> getErrors() { return errors; }
        public List<String> getWarnings() { return warnings; }
        public List<String> getSuggestions() { return suggestions; }

        public void addError(String error) {
            errors.add(error);
            valid = false;
        }

        public void addWarning(String warning) {
            warnings.add(warning);
        }

        public void addSuggestion(String suggestion) {
            suggestions.add(suggestion);
        }

        public boolean hasIssues() {
            return !errors.isEmpty() || !warnings.isEmpty();
        }
    }

    /**
     * 验证完整配置
     */
    public ValidationResult validateConfiguration(SecurityCheckerConfig config) {
        ValidationResult result = new ValidationResult();

        // 验证CVE配置
        validateCveConfig(config.getCve(), result);

        // 验证许可证配置
        validateLicenseConfig(config.getLicense(), result);

        // 验证报告配置
        validateReportConfig(config.getReport(), result);

        // 验证Maven配置
        validateMavenConfig(config.getMaven(), result);

        return result;
    }

    /**
     * 验证CVE配置
     */
    private void validateCveConfig(SecurityCheckerConfig.CveConfig config, ValidationResult result) {
        // 验证NVD API URL
        if (!isValidUrl(config.getNvdApiUrl())) {
            result.addError("无效的NVD API URL: " + config.getNvdApiUrl());
        }

        // 验证API Key
        if (config.getNvdApiKey() == null || config.getNvdApiKey().trim().isEmpty()) {
            result.addWarning("未设置NVD API Key，将使用较低的请求限制");
            result.addSuggestion("建议在 https://nvd.nist.gov/developers/request-an-api-key 申请API Key");
        } else if (config.getNvdApiKey().length() < 30) {
            result.addWarning("NVD API Key长度异常，请检查是否正确");
        }

        // 验证本地数据库路径
        validateDatabasePath(config.getLocalDbPath(), "CVE数据库", result);

        // 验证更新间隔
        if (config.getUpdateIntervalHours() < 1) {
            result.addError("CVE更新间隔不能小于1小时");
        } else if (config.getUpdateIntervalHours() < 6) {
            result.addWarning("CVE更新间隔过短，建议至少6小时");
        }

        // 验证超时时间
        if (config.getRequestTimeoutSeconds() < 5) {
            result.addError("请求超时时间不能小于5秒");
        }

        // 验证重试次数
        if (config.getMaxRetries() < 1) {
            result.addError("最大重试次数不能小于1");
        }

        // 验证批处理大小
        if (config.getBatchSize() < 100) {
            result.addWarning("批处理大小过小，可能影响性能");
        } else if (config.getBatchSize() > 10000) {
            result.addWarning("批处理大小过大，可能导致内存问题");
        }
    }

    /**
     * 验证许可证配置
     */
    private void validateLicenseConfig(SecurityCheckerConfig.LicenseConfig config, ValidationResult result) {
        // 验证SPDX URL
        if (!isValidUrl(config.getSpdxLicenseUrl())) {
            result.addError("无效的SPDX许可证URL: " + config.getSpdxLicenseUrl());
        }

        // 验证限制性许可证列表
        if (config.getRestrictedLicenses().isEmpty()) {
            result.addWarning("未配置限制性许可证列表");
            result.addSuggestion("建议配置GPL、AGPL等限制性许可证");
        }

        // 验证本地许可证数据库路径
        validateDatabasePath(config.getLocalLicenseDb(), "许可证数据库", result);
    }

    /**
     * 验证报告配置
     */
    private void validateReportConfig(SecurityCheckerConfig.ReportConfig config, ValidationResult result) {
        // 验证输出目录
        validateDirectoryPath(config.getOutputDir(), "报告输出目录", result, true);

        // 验证严重级别阈值
        String threshold = config.getSeverityThreshold();
        if (!isValidSeverityLevel(threshold)) {
            result.addError("无效的严重级别阈值: " + threshold + "，有效值: LOW, MEDIUM, HIGH, CRITICAL");
        }

        // 验证模板目录
        if (!config.getTemplateDir().startsWith("classpath:")) {
            validateDirectoryPath(config.getTemplateDir(), "模板目录", result, false);
        }
    }

    /**
     * 验证Maven配置
     */
    private void validateMavenConfig(SecurityCheckerConfig.MavenConfig config, ValidationResult result) {
        // 验证本地仓库路径
        String localRepo = expandPath(config.getLocalRepository());
        validateDirectoryPath(localRepo, "Maven本地仓库", result, false);

        // 验证中央仓库URL
        if (!isValidUrl(config.getCentralUrl())) {
            result.addError("无效的Maven中央仓库URL: " + config.getCentralUrl());
        }

        // 验证超时时间
        if (config.getTimeoutSeconds() < 5) {
            result.addError("Maven超时时间不能小于5秒");
        } else if (config.getTimeoutSeconds() > 300) {
            result.addWarning("Maven超时时间过长，可能影响用户体验");
        }
    }

    /**
     * 验证数据库路径
     */
    private void validateDatabasePath(String dbPath, String dbType, ValidationResult result) {
        if (dbPath == null || dbPath.trim().isEmpty()) {
            result.addError(dbType + "路径不能为空");
            return;
        }

        try {
            Path path = Paths.get(dbPath);
            Path parentDir = path.getParent();
            
            if (parentDir != null && !Files.exists(parentDir)) {
                result.addWarning(dbType + "目录不存在，将自动创建: " + parentDir);
            }
            
            // 检查是否有写权限
            if (parentDir != null && Files.exists(parentDir) && !Files.isWritable(parentDir)) {
                result.addError(dbType + "目录没有写权限: " + parentDir);
            }
        } catch (Exception e) {
            result.addError(dbType + "路径无效: " + dbPath + " (" + e.getMessage() + ")");
        }
    }

    /**
     * 验证目录路径
     */
    private void validateDirectoryPath(String dirPath, String dirType, ValidationResult result, boolean createIfNotExists) {
        if (dirPath == null || dirPath.trim().isEmpty()) {
            result.addError(dirType + "路径不能为空");
            return;
        }

        try {
            Path path = Paths.get(expandPath(dirPath));
            
            if (!Files.exists(path)) {
                if (createIfNotExists) {
                    result.addWarning(dirType + "不存在，将自动创建: " + path);
                } else {
                    result.addError(dirType + "不存在: " + path);
                }
            } else if (!Files.isDirectory(path)) {
                result.addError(dirType + "不是目录: " + path);
            } else if (!Files.isWritable(path)) {
                result.addError(dirType + "没有写权限: " + path);
            }
        } catch (Exception e) {
            result.addError(dirType + "路径无效: " + dirPath + " (" + e.getMessage() + ")");
        }
    }

    /**
     * 验证URL有效性
     */
    private boolean isValidUrl(String urlString) {
        if (urlString == null || urlString.trim().isEmpty()) {
            return false;
        }
        
        try {
            new URL(urlString);
            return true;
        } catch (MalformedURLException e) {
            return false;
        }
    }

    /**
     * 验证严重级别
     */
    private boolean isValidSeverityLevel(String level) {
        if (level == null) return false;
        String upperLevel = level.toUpperCase();
        return "LOW".equals(upperLevel) || "MEDIUM".equals(upperLevel) || 
               "HIGH".equals(upperLevel) || "CRITICAL".equals(upperLevel);
    }

    /**
     * 展开路径（处理~符号）
     */
    private String expandPath(String path) {
        if (path != null && path.startsWith("~")) {
            return System.getProperty("user.home") + path.substring(1);
        }
        return path;
    }

    /**
     * 打印验证结果
     */
    public void printValidationResult(ValidationResult result) {
        if (result.isValid() && !result.hasIssues()) {
            System.out.println("✅ 配置验证通过");
            return;
        }

        System.out.println("📋 配置验证结果:");
        System.out.println("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");

        if (!result.getErrors().isEmpty()) {
            System.out.println("❌ 错误:");
            for (String error : result.getErrors()) {
                System.out.println("   " + error);
            }
        }

        if (!result.getWarnings().isEmpty()) {
            System.out.println("⚠️  警告:");
            for (String warning : result.getWarnings()) {
                System.out.println("   " + warning);
            }
        }

        if (!result.getSuggestions().isEmpty()) {
            System.out.println("💡 建议:");
            for (String suggestion : result.getSuggestions()) {
                System.out.println("   " + suggestion);
            }
        }

        if (result.isValid()) {
            System.out.println("✅ 配置可用，但建议处理上述警告");
        } else {
            System.out.println("❌ 配置无效，请修复上述错误");
        }
    }
}
