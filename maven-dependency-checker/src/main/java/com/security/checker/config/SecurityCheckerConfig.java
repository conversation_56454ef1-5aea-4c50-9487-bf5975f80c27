package com.security.checker.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * 安全检查器配置类
 * 
 * 提供类型安全的配置属性绑定和验证
 * 
 * <AUTHOR>
 */
@Configuration
@ConfigurationProperties(prefix = "security-checker")
public class SecurityCheckerConfig {

    private CveConfig cve = new CveConfig();
    private LicenseConfig license = new LicenseConfig();
    private ReportConfig report = new ReportConfig();
    private MavenConfig maven = new MavenConfig();

    // Getters and Setters
    public CveConfig getCve() { return cve; }
    public void setCve(CveConfig cve) { this.cve = cve; }

    public LicenseConfig getLicense() { return license; }
    public void setLicense(LicenseConfig license) { this.license = license; }

    public ReportConfig getReport() { return report; }
    public void setReport(ReportConfig report) { this.report = report; }

    public MavenConfig getMaven() { return maven; }
    public void setMaven(MavenConfig maven) { this.maven = maven; }

    /**
     * CVE配置
     */
    public static class CveConfig {
        private String nvdApiUrl = "https://services.nvd.nist.gov/rest/json/cves/2.0";

        private String nvdApiKey = "";

        private String localDbPath = "./database/cve_data.db";

        private int updateIntervalHours = 24;

        private boolean enableFuzzyMatching = true;

        private int requestTimeoutSeconds = 30;

        private int maxRetries = 3;

        private int batchSize = 1000;

        // Getters and Setters
        public String getNvdApiUrl() { return nvdApiUrl; }
        public void setNvdApiUrl(String nvdApiUrl) { this.nvdApiUrl = nvdApiUrl; }

        public String getNvdApiKey() { return nvdApiKey; }
        public void setNvdApiKey(String nvdApiKey) { this.nvdApiKey = nvdApiKey; }

        public String getLocalDbPath() { return localDbPath; }
        public void setLocalDbPath(String localDbPath) { this.localDbPath = localDbPath; }

        public int getUpdateIntervalHours() { return updateIntervalHours; }
        public void setUpdateIntervalHours(int updateIntervalHours) { this.updateIntervalHours = updateIntervalHours; }

        public boolean isEnableFuzzyMatching() { return enableFuzzyMatching; }
        public void setEnableFuzzyMatching(boolean enableFuzzyMatching) { this.enableFuzzyMatching = enableFuzzyMatching; }

        public int getRequestTimeoutSeconds() { return requestTimeoutSeconds; }
        public void setRequestTimeoutSeconds(int requestTimeoutSeconds) { this.requestTimeoutSeconds = requestTimeoutSeconds; }

        public int getMaxRetries() { return maxRetries; }
        public void setMaxRetries(int maxRetries) { this.maxRetries = maxRetries; }

        public int getBatchSize() { return batchSize; }
        public void setBatchSize(int batchSize) { this.batchSize = batchSize; }
    }

    /**
     * 许可证配置
     */
    public static class LicenseConfig {
        private List<String> restrictedLicenses = java.util.Arrays.asList(
            "GPL-2.0", "GPL-3.0", "AGPL-3.0", "LGPL-2.1", "LGPL-3.0",
            "EUPL-1.2", "OSL-3.0", "SSPL-1.0", "BUSL-1.1", "Commons Clause"
        );

        private String spdxLicenseUrl = "https://raw.githubusercontent.com/spdx/license-list-data/master/json/licenses.json";

        private String localLicenseDb = "./database/license_data.db";
        
        private boolean enableFuzzyMatching = true;
        
        private boolean strictMode = false;

        // Getters and Setters
        public List<String> getRestrictedLicenses() { return restrictedLicenses; }
        public void setRestrictedLicenses(List<String> restrictedLicenses) { this.restrictedLicenses = restrictedLicenses; }

        public String getSpdxLicenseUrl() { return spdxLicenseUrl; }
        public void setSpdxLicenseUrl(String spdxLicenseUrl) { this.spdxLicenseUrl = spdxLicenseUrl; }

        public String getLocalLicenseDb() { return localLicenseDb; }
        public void setLocalLicenseDb(String localLicenseDb) { this.localLicenseDb = localLicenseDb; }

        public boolean isEnableFuzzyMatching() { return enableFuzzyMatching; }
        public void setEnableFuzzyMatching(boolean enableFuzzyMatching) { this.enableFuzzyMatching = enableFuzzyMatching; }

        public boolean isStrictMode() { return strictMode; }
        public void setStrictMode(boolean strictMode) { this.strictMode = strictMode; }
    }

    /**
     * 报告配置
     */
    public static class ReportConfig {
        private String outputDir = "./reports";

        private String templateDir = "classpath:/templates";

        private boolean includeTransitiveDeps = true;

        private String severityThreshold = "MEDIUM";
        
        private boolean enableDetailedReport = true;
        
        private boolean includeRecommendations = true;

        // Getters and Setters
        public String getOutputDir() { return outputDir; }
        public void setOutputDir(String outputDir) { this.outputDir = outputDir; }

        public String getTemplateDir() { return templateDir; }
        public void setTemplateDir(String templateDir) { this.templateDir = templateDir; }

        public boolean isIncludeTransitiveDeps() { return includeTransitiveDeps; }
        public void setIncludeTransitiveDeps(boolean includeTransitiveDeps) { this.includeTransitiveDeps = includeTransitiveDeps; }

        public String getSeverityThreshold() { return severityThreshold; }
        public void setSeverityThreshold(String severityThreshold) { this.severityThreshold = severityThreshold; }

        public boolean isEnableDetailedReport() { return enableDetailedReport; }
        public void setEnableDetailedReport(boolean enableDetailedReport) { this.enableDetailedReport = enableDetailedReport; }

        public boolean isIncludeRecommendations() { return includeRecommendations; }
        public void setIncludeRecommendations(boolean includeRecommendations) { this.includeRecommendations = includeRecommendations; }
    }

    /**
     * Maven配置
     */
    public static class MavenConfig {
        private String localRepository = "~/.m2/repository";

        private String centralUrl = "https://repo1.maven.org/maven2";

        private int timeoutSeconds = 30;
        
        private boolean enableOfflineMode = false;
        
        private boolean enableSnapshotUpdates = false;

        // Getters and Setters
        public String getLocalRepository() { return localRepository; }
        public void setLocalRepository(String localRepository) { this.localRepository = localRepository; }

        public String getCentralUrl() { return centralUrl; }
        public void setCentralUrl(String centralUrl) { this.centralUrl = centralUrl; }

        public int getTimeoutSeconds() { return timeoutSeconds; }
        public void setTimeoutSeconds(int timeoutSeconds) { this.timeoutSeconds = timeoutSeconds; }

        public boolean isEnableOfflineMode() { return enableOfflineMode; }
        public void setEnableOfflineMode(boolean enableOfflineMode) { this.enableOfflineMode = enableOfflineMode; }

        public boolean isEnableSnapshotUpdates() { return enableSnapshotUpdates; }
        public void setEnableSnapshotUpdates(boolean enableSnapshotUpdates) { this.enableSnapshotUpdates = enableSnapshotUpdates; }
    }
}
