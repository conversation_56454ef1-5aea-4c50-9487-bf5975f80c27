package com.security.checker.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Properties;

/**
 * 配置管理器
 * 
 * 功能：
 * 1. 支持多种配置源（命令行、环境变量、配置文件）
 * 2. 配置文件验证和加载
 * 3. 配置模板生成
 * 4. 配置优先级管理
 * 
 * <AUTHOR>
 */
@Component
public class ConfigurationManager {

    private static final Logger logger = LoggerFactory.getLogger(ConfigurationManager.class);
    
    private static final String DEFAULT_CONFIG_FILE = "maven-checker.yml";
    private static final String USER_CONFIG_DIR = System.getProperty("user.home") + "/.maven-checker";
    private static final String GLOBAL_CONFIG_FILE = USER_CONFIG_DIR + "/" + DEFAULT_CONFIG_FILE;
    
    @Autowired
    private Environment environment;
    
    @Autowired
    private SecurityCheckerConfig config;

    /**
     * 初始化配置管理器
     */
    public void initialize() {
        logger.info("初始化配置管理器...");
        
        // 创建用户配置目录
        createUserConfigDirectory();
        
        // 生成默认配置文件（如果不存在）
        generateDefaultConfigIfNotExists();
        
        // 验证配置
        validateConfiguration();
        
        logger.info("配置管理器初始化完成");
    }

    /**
     * 创建用户配置目录
     */
    private void createUserConfigDirectory() {
        try {
            Path configDir = Paths.get(USER_CONFIG_DIR);
            if (!Files.exists(configDir)) {
                Files.createDirectories(configDir);
                logger.info("创建用户配置目录: {}", USER_CONFIG_DIR);
            }
        } catch (IOException e) {
            logger.warn("无法创建用户配置目录: {}", e.getMessage());
        }
    }

    /**
     * 生成默认配置文件
     */
    public void generateDefaultConfigIfNotExists() {
        File configFile = new File(GLOBAL_CONFIG_FILE);
        if (!configFile.exists()) {
            try {
                generateDefaultConfigFile(configFile);
                logger.info("生成默认配置文件: {}", GLOBAL_CONFIG_FILE);
            } catch (IOException e) {
                logger.warn("无法生成默认配置文件: {}", e.getMessage());
            }
        }
    }

    /**
     * 生成默认配置文件内容
     */
    private void generateDefaultConfigFile(File configFile) throws IOException {
        String defaultConfig = generateDefaultConfigContent();
        Files.write(configFile.toPath(), defaultConfig.getBytes());
    }

    /**
     * 生成默认配置内容
     */
    private String generateDefaultConfigContent() {
        StringBuilder sb = new StringBuilder();
        sb.append("# Maven依赖安全检查工具配置文件\n");
        sb.append("# 配置优先级: 命令行参数 > 环境变量 > 项目配置文件 > 用户配置文件 > 默认配置\n\n");
        sb.append("security-checker:\n");
        sb.append("  # CVE漏洞检查配置\n");
        sb.append("  cve:\n");
        sb.append("    # NVD API配置\n");
        sb.append("    nvd-api-url: https://services.nvd.nist.gov/rest/json/cves/2.0\n");
        sb.append("    # NVD API Key (建议设置以获得更高的请求限制)\n");
        sb.append("    # 获取方式: https://nvd.nist.gov/developers/request-an-api-key\n");
        sb.append("    nvd-api-key: ${NVD_API_KEY:}\n");
        sb.append("    # 本地数据库路径\n");
        sb.append("    local-db-path: ./database/cve_data.db\n");
        sb.append("    # 数据更新间隔（小时）\n");
        sb.append("    update-interval-hours: 24\n");
        sb.append("    # 启用模糊匹配\n");
        sb.append("    enable-fuzzy-matching: true\n");
        sb.append("    # 请求超时时间（秒）\n");
        sb.append("    request-timeout-seconds: 30\n");
        sb.append("    # 最大重试次数\n");
        sb.append("    max-retries: 3\n");
        sb.append("    # 批处理大小\n");
        sb.append("    batch-size: 1000\n\n");

        sb.append("  # 许可证检查配置\n");
        sb.append("  license:\n");
        sb.append("    # 限制性许可证列表\n");
        sb.append("    restricted-licenses:\n");
        sb.append("      - GPL-2.0\n");
        sb.append("      - GPL-3.0\n");
        sb.append("      - AGPL-3.0\n");
        sb.append("      - LGPL-2.1\n");
        sb.append("      - LGPL-3.0\n");
        sb.append("      - EUPL-1.2\n");
        sb.append("      - OSL-3.0\n");
        sb.append("      - SSPL-1.0\n");
        sb.append("      - BUSL-1.1\n");
        sb.append("      - Commons Clause\n");
        sb.append("      - CPAL-1.0\n");
        sb.append("      - CDDL-1.0\n");
        sb.append("      - CDDL-1.1\n");
        sb.append("      - EPL-1.0\n");
        sb.append("      - EPL-2.0\n");
        sb.append("      - MPL-2.0\n");
        sb.append("    # SPDX许可证数据源\n");
        sb.append("    spdx-license-url: https://raw.githubusercontent.com/spdx/license-list-data/master/json/licenses.json\n");
        sb.append("    # 本地许可证数据库\n");
        sb.append("    local-license-db: ./database/license_data.db\n");
        sb.append("    # 启用模糊匹配\n");
        sb.append("    enable-fuzzy-matching: true\n");
        sb.append("    # 严格模式（未知许可证视为风险）\n");
        sb.append("    strict-mode: false\n\n");

        sb.append("  # 报告生成配置\n");
        sb.append("  report:\n");
        sb.append("    # 报告输出目录\n");
        sb.append("    output-dir: ./reports\n");
        sb.append("    # 模板目录\n");
        sb.append("    template-dir: classpath:/templates\n");
        sb.append("    # 包含传递依赖\n");
        sb.append("    include-transitive-deps: true\n");
        sb.append("    # 最低严重级别阈值\n");
        sb.append("    severity-threshold: MEDIUM\n");
        sb.append("    # 启用详细报告\n");
        sb.append("    enable-detailed-report: true\n");
        sb.append("    # 包含修复建议\n");
        sb.append("    include-recommendations: true\n\n");

        sb.append("  # Maven配置\n");
        sb.append("  maven:\n");
        sb.append("    # 本地仓库路径\n");
        sb.append("    local-repository: ~/.m2/repository\n");
        sb.append("    # 中央仓库URL\n");
        sb.append("    central-url: https://repo1.maven.org/maven2\n");
        sb.append("    # 超时时间（秒）\n");
        sb.append("    timeout-seconds: 30\n");
        sb.append("    # 离线模式\n");
        sb.append("    enable-offline-mode: false\n");
        sb.append("    # 启用快照更新\n");
        sb.append("    enable-snapshot-updates: false\n");

        return sb.toString();
    }

    /**
     * 加载用户配置文件
     */
    public Properties loadUserConfig() {
        Properties userConfig = new Properties();
        File configFile = new File(GLOBAL_CONFIG_FILE);
        
        if (configFile.exists()) {
            try (FileInputStream fis = new FileInputStream(configFile)) {
                userConfig.load(fis);
                logger.info("加载用户配置文件: {}", GLOBAL_CONFIG_FILE);
            } catch (IOException e) {
                logger.warn("无法加载用户配置文件: {}", e.getMessage());
            }
        }
        
        return userConfig;
    }

    /**
     * 验证配置
     */
    private void validateConfiguration() {
        logger.info("验证配置...");
        
        // 验证CVE配置
        validateCveConfig();
        
        // 验证许可证配置
        validateLicenseConfig();
        
        // 验证报告配置
        validateReportConfig();
        
        // 验证Maven配置
        validateMavenConfig();
        
        logger.info("配置验证完成");
    }

    /**
     * 验证CVE配置
     */
    private void validateCveConfig() {
        SecurityCheckerConfig.CveConfig cveConfig = config.getCve();
        
        if (cveConfig.getNvdApiKey() == null || cveConfig.getNvdApiKey().trim().isEmpty()) {
            logger.warn("未设置NVD API Key，将使用较低的请求限制");
        }
        
        if (cveConfig.getUpdateIntervalHours() < 1) {
            logger.warn("CVE更新间隔过短，建议至少1小时");
        }
        
        // 验证本地数据库路径
        validateDatabasePath(cveConfig.getLocalDbPath(), "CVE数据库");
    }

    /**
     * 验证许可证配置
     */
    private void validateLicenseConfig() {
        SecurityCheckerConfig.LicenseConfig licenseConfig = config.getLicense();
        
        if (licenseConfig.getRestrictedLicenses().isEmpty()) {
            logger.warn("未配置限制性许可证列表");
        }
        
        // 验证本地数据库路径
        validateDatabasePath(licenseConfig.getLocalLicenseDb(), "许可证数据库");
    }

    /**
     * 验证报告配置
     */
    private void validateReportConfig() {
        SecurityCheckerConfig.ReportConfig reportConfig = config.getReport();
        
        // 验证输出目录
        try {
            Path outputDir = Paths.get(reportConfig.getOutputDir());
            if (!Files.exists(outputDir)) {
                Files.createDirectories(outputDir);
                logger.info("创建报告输出目录: {}", reportConfig.getOutputDir());
            }
        } catch (IOException e) {
            logger.warn("无法创建报告输出目录: {}", e.getMessage());
        }
    }

    /**
     * 验证Maven配置
     */
    private void validateMavenConfig() {
        SecurityCheckerConfig.MavenConfig mavenConfig = config.getMaven();
        
        // 验证本地仓库路径
        String localRepo = mavenConfig.getLocalRepository();
        if (localRepo.startsWith("~")) {
            localRepo = System.getProperty("user.home") + localRepo.substring(1);
        }
        
        Path repoPath = Paths.get(localRepo);
        if (!Files.exists(repoPath)) {
            logger.warn("Maven本地仓库不存在: {}", localRepo);
        }
    }

    /**
     * 验证数据库路径
     */
    private void validateDatabasePath(String dbPath, String dbType) {
        try {
            Path path = Paths.get(dbPath);
            Path parentDir = path.getParent();
            if (parentDir != null && !Files.exists(parentDir)) {
                Files.createDirectories(parentDir);
                logger.info("创建{}目录: {}", dbType, parentDir);
            }
        } catch (IOException e) {
            logger.warn("无法创建{}目录: {}", dbType, e.getMessage());
        }
    }

    /**
     * 获取配置值（支持多种来源）
     */
    public String getConfigValue(String key, String defaultValue) {
        // 优先级: 环境变量 > 系统属性 > Spring配置
        String value = System.getenv(key.toUpperCase().replace(".", "_"));
        if (value != null) {
            return value;
        }
        
        value = System.getProperty(key);
        if (value != null) {
            return value;
        }
        
        return environment.getProperty(key, defaultValue);
    }

    /**
     * 打印当前配置信息
     */
    public void printConfigurationInfo() {
        logger.info("当前配置信息:");
        logger.info("  用户配置目录: {}", USER_CONFIG_DIR);
        logger.info("  全局配置文件: {}", GLOBAL_CONFIG_FILE);
        logger.info("  CVE数据库路径: {}", config.getCve().getLocalDbPath());
        logger.info("  许可证数据库路径: {}", config.getLicense().getLocalLicenseDb());
        logger.info("  报告输出目录: {}", config.getReport().getOutputDir());
        logger.info("  Maven本地仓库: {}", config.getMaven().getLocalRepository());
    }

    // Getters
    public String getUserConfigDir() { return USER_CONFIG_DIR; }
    public String getGlobalConfigFile() { return GLOBAL_CONFIG_FILE; }
}
