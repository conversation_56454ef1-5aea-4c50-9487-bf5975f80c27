package com.security.checker;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.CommandLineRunner;
import org.springframework.beans.factory.annotation.Autowired;
import com.security.checker.cli.MavenCheckerCLI;

/**
 * Maven Dependency Security Checker Application
 *
 * 离线Maven依赖安全检查工具主应用类
 * 支持CVE漏洞检查和许可证合规性检查
 *
 * <AUTHOR>
 * @version 1.0
 */
@SpringBootApplication
public class App implements CommandLineRunner {

    @Autowired
    private MavenCheckerCLI mavenCheckerCLI;

    public static void main(String[] args) {
        // 设置系统属性，禁用Spring Boot的Web环境
        System.setProperty("spring.main.web-application-type", "none");

        SpringApplication app = new SpringApplication(App.class);
        app.setWebApplicationType(org.springframework.boot.WebApplicationType.NONE);
        app.run(args);
    }

    @Override
    public void run(String... args) throws Exception {
        // 启动命令行界面
        mavenCheckerCLI.execute(args);
    }
}
