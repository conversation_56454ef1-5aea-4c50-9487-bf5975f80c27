package com.security.checker.repository;

import com.security.checker.entity.ComponentLicenseEntity;
import com.security.checker.entity.LicenseEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 组件许可证关联数据访问接口
 */
@Repository
public interface ComponentLicenseRepository extends JpaRepository<ComponentLicenseEntity, Long> {
    
    /**
     * 根据组件坐标查找许可证
     */
    List<ComponentLicenseEntity> findByGroupIdAndArtifactIdAndVersion(String groupId, String artifactId, String version);
    
    /**
     * 根据组件GAV（不包含版本）查找许可证
     */
    List<ComponentLicenseEntity> findByGroupIdAndArtifactId(String groupId, String artifactId);
    
    /**
     * 根据许可证查找组件
     */
    List<ComponentLicenseEntity> findByLicense(LicenseEntity license);
    
    /**
     * 根据许可证ID查找组件
     */
    @Query("SELECT cl FROM ComponentLicenseEntity cl WHERE cl.license.licenseId = :licenseId")
    List<ComponentLicenseEntity> findByLicenseId(@Param("licenseId") String licenseId);
    
    /**
     * 根据检测方法查找组件许可证
     */
    List<ComponentLicenseEntity> findByDetectionMethod(String detectionMethod);
    
    /**
     * 根据置信度查找组件许可证
     */
    List<ComponentLicenseEntity> findByConfidence(String confidence);
    
    /**
     * 查找主要许可证
     */
    List<ComponentLicenseEntity> findByPrimaryLicense(Boolean primaryLicense);
    
    /**
     * 根据组件类型查找许可证
     */
    List<ComponentLicenseEntity> findByType(String type);
    
    /**
     * 根据数据源查找组件许可证
     */
    List<ComponentLicenseEntity> findBySource(String source);
    
    /**
     * 查找指定组件的主要许可证
     */
    @Query("SELECT cl FROM ComponentLicenseEntity cl WHERE cl.groupId = :groupId " +
           "AND cl.artifactId = :artifactId AND cl.version = :version AND cl.primaryLicense = true")
    Optional<ComponentLicenseEntity> findPrimaryLicense(@Param("groupId") String groupId, 
                                                       @Param("artifactId") String artifactId, 
                                                       @Param("version") String version);
    
    /**
     * 查找使用商业不友好许可证的组件
     */
    @Query("SELECT cl FROM ComponentLicenseEntity cl WHERE cl.license.commercialFriendly = false")
    List<ComponentLicenseEntity> findComponentsWithUnfriendlyLicenses();
    
    /**
     * 查找使用Copyleft许可证的组件
     */
    @Query("SELECT cl FROM ComponentLicenseEntity cl WHERE cl.license.copyleftType != 'NONE'")
    List<ComponentLicenseEntity> findComponentsWithCopyleftLicenses();
    
    /**
     * 查找使用高风险许可证的组件
     */
    @Query("SELECT cl FROM ComponentLicenseEntity cl WHERE cl.license.riskLevel IN ('HIGH', 'CRITICAL')")
    List<ComponentLicenseEntity> findComponentsWithHighRiskLicenses();
    
    /**
     * 统计各许可证的使用次数
     */
    @Query("SELECT cl.license.licenseId, cl.license.name, COUNT(cl) FROM ComponentLicenseEntity cl " +
           "GROUP BY cl.license.licenseId, cl.license.name ORDER BY COUNT(cl) DESC")
    List<Object[]> countComponentsByLicense();
    
    /**
     * 统计各检测方法的使用次数
     */
    @Query("SELECT cl.detectionMethod, COUNT(cl) FROM ComponentLicenseEntity cl " +
           "GROUP BY cl.detectionMethod ORDER BY COUNT(cl) DESC")
    List<Object[]> countByDetectionMethod();
    
    /**
     * 统计各置信度的分布
     */
    @Query("SELECT cl.confidence, COUNT(cl) FROM ComponentLicenseEntity cl " +
           "GROUP BY cl.confidence ORDER BY COUNT(cl) DESC")
    List<Object[]> countByConfidence();
    
    /**
     * 查找有多个许可证的组件
     */
    @Query("SELECT cl.groupId, cl.artifactId, cl.version, COUNT(cl) FROM ComponentLicenseEntity cl " +
           "GROUP BY cl.groupId, cl.artifactId, cl.version HAVING COUNT(cl) > 1")
    List<Object[]> findComponentsWithMultipleLicenses();
    
    /**
     * 查找没有主要许可证的组件
     */
    @Query("SELECT DISTINCT cl.groupId, cl.artifactId, cl.version FROM ComponentLicenseEntity cl " +
           "WHERE NOT EXISTS (SELECT 1 FROM ComponentLicenseEntity cl2 " +
           "WHERE cl2.groupId = cl.groupId AND cl2.artifactId = cl.artifactId " +
           "AND cl2.version = cl.version AND cl2.primaryLicense = true)")
    List<Object[]> findComponentsWithoutPrimaryLicense();
    
    /**
     * 根据声明的许可证名称查找组件
     */
    @Query("SELECT cl FROM ComponentLicenseEntity cl WHERE LOWER(cl.declaredLicense) LIKE LOWER(CONCAT('%', :licenseName, '%'))")
    List<ComponentLicenseEntity> findByDeclaredLicenseContaining(@Param("licenseName") String licenseName);
    
    /**
     * 查找高置信度的许可证检测
     */
    @Query("SELECT cl FROM ComponentLicenseEntity cl WHERE cl.confidence = 'HIGH'")
    List<ComponentLicenseEntity> findHighConfidenceDetections();
    
    /**
     * 查找低置信度的许可证检测
     */
    @Query("SELECT cl FROM ComponentLicenseEntity cl WHERE cl.confidence = 'LOW'")
    List<ComponentLicenseEntity> findLowConfidenceDetections();
    
    /**
     * 根据许可证文件路径查找组件
     */
    List<ComponentLicenseEntity> findByLicenseFilePathIsNotNull();
    
    /**
     * 查找从JAR文件中检测到许可证的组件
     */
    @Query("SELECT cl FROM ComponentLicenseEntity cl WHERE cl.detectionMethod IN ('JAR_MANIFEST', 'LICENSE_FILE')")
    List<ComponentLicenseEntity> findJarBasedDetections();
    
    /**
     * 查找从POM文件中检测到许可证的组件
     */
    @Query("SELECT cl FROM ComponentLicenseEntity cl WHERE cl.detectionMethod = 'POM_DECLARED'")
    List<ComponentLicenseEntity> findPomBasedDetections();
    
    /**
     * 删除指定组件的所有许可证关联
     */
    void deleteByGroupIdAndArtifactIdAndVersion(String groupId, String artifactId, String version);
    
    /**
     * 删除指定许可证的所有组件关联
     */
    void deleteByLicense(LicenseEntity license);
    
    /**
     * 检查组件是否存在许可证信息
     */
    boolean existsByGroupIdAndArtifactIdAndVersion(String groupId, String artifactId, String version);
    
    /**
     * 查找指定组件的所有许可证（包括非主要许可证）
     */
    @Query("SELECT cl FROM ComponentLicenseEntity cl WHERE cl.groupId = :groupId " +
           "AND cl.artifactId = :artifactId AND cl.version = :version ORDER BY cl.primaryLicense DESC, cl.confidence DESC")
    List<ComponentLicenseEntity> findAllLicensesForComponent(@Param("groupId") String groupId, 
                                                            @Param("artifactId") String artifactId, 
                                                            @Param("version") String version);
    
    /**
     * 根据组件坐标模式查找许可证
     */
    @Query("SELECT cl FROM ComponentLicenseEntity cl WHERE " +
           "(:groupId IS NULL OR cl.groupId LIKE CONCAT('%', :groupId, '%')) " +
           "AND (:artifactId IS NULL OR cl.artifactId LIKE CONCAT('%', :artifactId, '%'))")
    List<ComponentLicenseEntity> findByComponentPattern(@Param("groupId") String groupId, 
                                                       @Param("artifactId") String artifactId);
    
    /**
     * 查找需要源码公开的组件
     */
    @Query("SELECT cl FROM ComponentLicenseEntity cl WHERE cl.license.requiresSourceDisclosure = true")
    List<ComponentLicenseEntity> findComponentsRequiringSourceDisclosure();
    
    /**
     * 查找不允许商业使用的组件
     */
    @Query("SELECT cl FROM ComponentLicenseEntity cl WHERE cl.license.allowsCommercialUse = false")
    List<ComponentLicenseEntity> findComponentsProhibitingCommercialUse();
}
