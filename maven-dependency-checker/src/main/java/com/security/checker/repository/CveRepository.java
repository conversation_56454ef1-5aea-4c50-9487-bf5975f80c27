package com.security.checker.repository;

import com.security.checker.entity.CveEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * CVE漏洞数据访问接口
 */
@Repository
public interface CveRepository extends JpaRepository<CveEntity, Long> {
    
    /**
     * 根据CVE ID查找漏洞
     */
    Optional<CveEntity> findByCveId(String cveId);
    
    /**
     * 检查CVE ID是否存在
     */
    boolean existsByCveId(String cveId);
    
    /**
     * 根据严重程度查找漏洞
     */
    List<CveEntity> findBySeverity(String severity);
    
    /**
     * 根据严重程度列表查找漏洞
     */
    List<CveEntity> findBySeverityIn(List<String> severities);
    
    /**
     * 根据CVSS分数范围查找漏洞
     */
    List<CveEntity> findByCvssScoreBetween(Double minScore, Double maxScore);
    
    /**
     * 根据CVSS分数大于等于指定值查找漏洞
     */
    List<CveEntity> findByCvssScoreGreaterThanEqual(Double minScore);
    
    /**
     * 根据发布日期范围查找漏洞
     */
    List<CveEntity> findByPublishedDateBetween(LocalDateTime startDate, LocalDateTime endDate);
    
    /**
     * 根据最后修改日期查找漏洞
     */
    List<CveEntity> findByLastModifiedDateAfter(LocalDateTime lastModified);
    
    /**
     * 根据状态查找漏洞
     */
    List<CveEntity> findByStatus(String status);
    
    /**
     * 查找最近发布的漏洞
     */
    @Query("SELECT c FROM CveEntity c ORDER BY c.publishedDate DESC")
    Page<CveEntity> findRecentlyPublished(Pageable pageable);
    
    /**
     * 查找高危漏洞（CRITICAL和HIGH）
     */
    @Query("SELECT c FROM CveEntity c WHERE c.severity IN ('CRITICAL', 'HIGH') ORDER BY c.cvssScore DESC")
    List<CveEntity> findHighSeverityVulnerabilities();
    
    /**
     * 根据产品名称查找相关漏洞
     */
    @Query("SELECT DISTINCT c FROM CveEntity c " +
           "JOIN c.affectedComponents ac " +
           "WHERE LOWER(ac.product) LIKE LOWER(CONCAT('%', :product, '%'))")
    List<CveEntity> findByAffectedProduct(@Param("product") String product);
    
    /**
     * 根据供应商和产品查找漏洞
     */
    @Query("SELECT DISTINCT c FROM CveEntity c " +
           "JOIN c.affectedComponents ac " +
           "WHERE LOWER(ac.vendor) LIKE LOWER(CONCAT('%', :vendor, '%')) " +
           "AND LOWER(ac.product) LIKE LOWER(CONCAT('%', :product, '%'))")
    List<CveEntity> findByVendorAndProduct(@Param("vendor") String vendor, @Param("product") String product);
    
    /**
     * 根据产品和版本查找漏洞
     */
    @Query("SELECT DISTINCT c FROM CveEntity c " +
           "JOIN c.affectedComponents ac " +
           "WHERE LOWER(ac.product) = LOWER(:product) " +
           "AND ac.vulnerable = true " +
           "AND (" +
           "  (ac.versionStartIncluding IS NULL OR :version >= ac.versionStartIncluding) " +
           "  AND (ac.versionEndExcluding IS NULL OR :version < ac.versionEndExcluding) " +
           "  AND (ac.versionStartExcluding IS NULL OR :version > ac.versionStartExcluding) " +
           "  AND (ac.versionEndIncluding IS NULL OR :version <= ac.versionEndIncluding)" +
           ")")
    List<CveEntity> findByProductAndVersion(@Param("product") String product, @Param("version") String version);
    
    /**
     * 统计各严重程度的漏洞数量
     */
    @Query("SELECT c.severity, COUNT(c) FROM CveEntity c GROUP BY c.severity")
    List<Object[]> countBySeverity();
    
    /**
     * 统计指定日期范围内的漏洞数量
     */
    @Query("SELECT COUNT(c) FROM CveEntity c WHERE c.publishedDate BETWEEN :startDate AND :endDate")
    Long countByDateRange(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate);
    
    /**
     * 查找需要更新的漏洞（基于最后修改时间）
     */
    @Query("SELECT c FROM CveEntity c WHERE c.lastModifiedDate > c.updatedAt")
    List<CveEntity> findOutdatedCves();
    
    /**
     * 根据描述关键词搜索漏洞
     */
    @Query("SELECT c FROM CveEntity c WHERE LOWER(c.description) LIKE LOWER(CONCAT('%', :keyword, '%'))")
    List<CveEntity> searchByDescription(@Param("keyword") String keyword);
    
    /**
     * 查找指定CVSS分数以上的漏洞数量
     */
    @Query("SELECT COUNT(c) FROM CveEntity c WHERE c.cvssScore >= :minScore")
    Long countByCvssScoreGreaterThanEqual(@Param("minScore") Double minScore);
    
    /**
     * 查找最新的CVE ID（用于增量同步）
     */
    @Query("SELECT c.cveId FROM CveEntity c ORDER BY c.publishedDate DESC")
    Page<String> findLatestCveIds(Pageable pageable);
    
    /**
     * 根据数据源查找漏洞
     */
    List<CveEntity> findBySource(String source);
    
    /**
     * 删除指定日期之前的旧漏洞数据
     */
    @Query("DELETE FROM CveEntity c WHERE c.publishedDate < :cutoffDate")
    void deleteOldCves(@Param("cutoffDate") LocalDateTime cutoffDate);
    
    /**
     * 查找重复的CVE（相同CVE ID）
     */
    @Query("SELECT c.cveId, COUNT(c) FROM CveEntity c GROUP BY c.cveId HAVING COUNT(c) > 1")
    List<Object[]> findDuplicateCves();
    
    /**
     * 批量查找CVE
     */
    List<CveEntity> findByCveIdIn(List<String> cveIds);
    
    /**
     * 查找没有受影响组件的CVE
     */
    @Query("SELECT c FROM CveEntity c WHERE c.affectedComponents IS EMPTY")
    List<CveEntity> findCvesWithoutAffectedComponents();
    
    /**
     * 根据创建时间范围查找CVE
     */
    List<CveEntity> findByCreatedAtBetween(LocalDateTime startDate, LocalDateTime endDate);
}
