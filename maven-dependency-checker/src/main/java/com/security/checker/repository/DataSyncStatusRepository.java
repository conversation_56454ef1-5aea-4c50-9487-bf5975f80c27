package com.security.checker.repository;

import com.security.checker.entity.DataSyncStatusEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 数据同步状态数据访问接口
 */
@Repository
public interface DataSyncStatusRepository extends JpaRepository<DataSyncStatusEntity, Long> {
    
    /**
     * 根据数据类型和数据源查找同步状态
     */
    Optional<DataSyncStatusEntity> findByDataTypeAndDataSource(String dataType, String dataSource);
    
    /**
     * 根据数据类型查找同步状态
     */
    List<DataSyncStatusEntity> findByDataType(String dataType);
    
    /**
     * 根据数据源查找同步状态
     */
    List<DataSyncStatusEntity> findByDataSource(String dataSource);
    
    /**
     * 根据同步状态查找记录
     */
    List<DataSyncStatusEntity> findBySyncStatus(String syncStatus);
    
    /**
     * 查找正在同步的记录
     */
    @Query("SELECT ds FROM DataSyncStatusEntity ds WHERE ds.syncStatus = 'IN_PROGRESS'")
    List<DataSyncStatusEntity> findActiveSyncs();
    
    /**
     * 查找同步失败的记录
     */
    @Query("SELECT ds FROM DataSyncStatusEntity ds WHERE ds.syncStatus = 'FAILED'")
    List<DataSyncStatusEntity> findFailedSyncs();
    
    /**
     * 查找需要同步的记录
     */
    @Query("SELECT ds FROM DataSyncStatusEntity ds WHERE ds.autoSyncEnabled = true " +
           "AND (ds.lastSyncTime IS NULL OR ds.lastSyncTime < :cutoffTime)")
    List<DataSyncStatusEntity> findSyncsNeeded(@Param("cutoffTime") LocalDateTime cutoffTime);
    
    /**
     * 查找启用自动同步的记录
     */
    List<DataSyncStatusEntity> findByAutoSyncEnabled(Boolean autoSyncEnabled);
    
    /**
     * 根据最后同步时间范围查找记录
     */
    List<DataSyncStatusEntity> findByLastSyncTimeBetween(LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 查找最后同步时间在指定时间之前的记录
     */
    List<DataSyncStatusEntity> findByLastSyncTimeBefore(LocalDateTime cutoffTime);
    
    /**
     * 查找从未同步过的记录
     */
    @Query("SELECT ds FROM DataSyncStatusEntity ds WHERE ds.lastSyncTime IS NULL")
    List<DataSyncStatusEntity> findNeverSynced();
    
    /**
     * 查找可以重试的失败同步
     */
    @Query("SELECT ds FROM DataSyncStatusEntity ds WHERE ds.syncStatus = 'FAILED' " +
           "AND ds.retryCount < ds.maxRetries")
    List<DataSyncStatusEntity> findRetryableSyncs();
    
    /**
     * 查找超过最大重试次数的同步
     */
    @Query("SELECT ds FROM DataSyncStatusEntity ds WHERE ds.syncStatus = 'FAILED' " +
           "AND ds.retryCount >= ds.maxRetries")
    List<DataSyncStatusEntity> findExhaustedRetrySyncs();
    
    /**
     * 根据数据版本查找记录
     */
    Optional<DataSyncStatusEntity> findByDataVersion(String dataVersion);
    
    /**
     * 查找指定时间间隔内的同步记录
     */
    @Query("SELECT ds FROM DataSyncStatusEntity ds WHERE ds.syncIntervalHours = :intervalHours")
    List<DataSyncStatusEntity> findBySyncInterval(@Param("intervalHours") Integer intervalHours);
    
    /**
     * 统计各同步状态的数量
     */
    @Query("SELECT ds.syncStatus, COUNT(ds) FROM DataSyncStatusEntity ds GROUP BY ds.syncStatus")
    List<Object[]> countBySyncStatus();
    
    /**
     * 统计各数据类型的同步状态
     */
    @Query("SELECT ds.dataType, ds.syncStatus, COUNT(ds) FROM DataSyncStatusEntity ds " +
           "GROUP BY ds.dataType, ds.syncStatus")
    List<Object[]> countByDataTypeAndSyncStatus();
    
    /**
     * 查找最近的同步记录
     */
    @Query("SELECT ds FROM DataSyncStatusEntity ds ORDER BY ds.lastSyncTime DESC")
    List<DataSyncStatusEntity> findRecentSyncs();
    
    /**
     * 查找同步时间最长的记录
     */
    @Query("SELECT ds FROM DataSyncStatusEntity ds WHERE ds.syncStartTime IS NOT NULL " +
           "AND ds.syncEndTime IS NOT NULL ORDER BY (ds.syncEndTime - ds.syncStartTime) DESC")
    List<DataSyncStatusEntity> findLongestSyncs();
    
    /**
     * 查找成功的同步记录
     */
    @Query("SELECT ds FROM DataSyncStatusEntity ds WHERE ds.syncStatus = 'SUCCESS' " +
           "ORDER BY ds.lastSyncTime DESC")
    List<DataSyncStatusEntity> findSuccessfulSyncs();
    
    /**
     * 查找有错误消息的同步记录
     */
    @Query("SELECT ds FROM DataSyncStatusEntity ds WHERE ds.errorMessage IS NOT NULL " +
           "AND ds.errorMessage != ''")
    List<DataSyncStatusEntity> findSyncsWithErrors();
    
    /**
     * 根据进度百分比查找记录
     */
    List<DataSyncStatusEntity> findByProgressPercentage(Integer progressPercentage);
    
    /**
     * 查找进度不完整的同步
     */
    @Query("SELECT ds FROM DataSyncStatusEntity ds WHERE ds.progressPercentage < 100 " +
           "AND ds.syncStatus = 'IN_PROGRESS'")
    List<DataSyncStatusEntity> findIncompleteSyncs();
    
    /**
     * 查找指定数据类型的最新同步状态
     */
    @Query("SELECT ds FROM DataSyncStatusEntity ds WHERE ds.dataType = :dataType " +
           "ORDER BY ds.lastSyncTime DESC")
    List<DataSyncStatusEntity> findLatestByDataType(@Param("dataType") String dataType);
    
    /**
     * 查找指定数据源的最新同步状态
     */
    @Query("SELECT ds FROM DataSyncStatusEntity ds WHERE ds.dataSource = :dataSource " +
           "ORDER BY ds.lastSyncTime DESC")
    List<DataSyncStatusEntity> findLatestByDataSource(@Param("dataSource") String dataSource);
    
    /**
     * 删除旧的同步记录
     */
    @Query("DELETE FROM DataSyncStatusEntity ds WHERE ds.createdAt < :cutoffDate")
    void deleteOldSyncRecords(@Param("cutoffDate") LocalDateTime cutoffDate);
    
    /**
     * 查找同步配置不为空的记录
     */
    @Query("SELECT ds FROM DataSyncStatusEntity ds WHERE ds.syncConfig IS NOT NULL " +
           "AND ds.syncConfig != ''")
    List<DataSyncStatusEntity> findSyncsWithConfig();
    
    /**
     * 根据总记录数范围查找同步
     */
    List<DataSyncStatusEntity> findByTotalRecordsBetween(Long minRecords, Long maxRecords);
    
    /**
     * 查找有新记录的同步
     */
    @Query("SELECT ds FROM DataSyncStatusEntity ds WHERE ds.newRecords > 0")
    List<DataSyncStatusEntity> findSyncsWithNewRecords();
    
    /**
     * 查找有更新记录的同步
     */
    @Query("SELECT ds FROM DataSyncStatusEntity ds WHERE ds.updatedRecords > 0")
    List<DataSyncStatusEntity> findSyncsWithUpdatedRecords();
    
    /**
     * 查找有失败记录的同步
     */
    @Query("SELECT ds FROM DataSyncStatusEntity ds WHERE ds.failedRecords > 0")
    List<DataSyncStatusEntity> findSyncsWithFailedRecords();
    
    /**
     * 计算平均同步时间（使用H2兼容的函数）
     */
    @Query("SELECT AVG(DATEDIFF('SECOND', ds.syncStartTime, ds.syncEndTime)) " +
           "FROM DataSyncStatusEntity ds WHERE ds.syncStartTime IS NOT NULL " +
           "AND ds.syncEndTime IS NOT NULL AND ds.syncStatus = 'SUCCESS'")
    Double calculateAverageSyncDuration();
    
    /**
     * 查找下次同步时间已到的记录
     */
    @Query("SELECT ds FROM DataSyncStatusEntity ds WHERE ds.nextSyncTime IS NOT NULL " +
           "AND ds.nextSyncTime <= :currentTime AND ds.autoSyncEnabled = true")
    List<DataSyncStatusEntity> findDueSyncs(@Param("currentTime") LocalDateTime currentTime);
}
