package com.security.checker.repository;

import com.security.checker.entity.CveAffectedComponentEntity;
import com.security.checker.entity.CveEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * CVE受影响组件数据访问接口
 */
@Repository
public interface CveAffectedComponentRepository extends JpaRepository<CveAffectedComponentEntity, Long> {
    
    /**
     * 根据CVE查找受影响组件
     */
    List<CveAffectedComponentEntity> findByCve(CveEntity cve);
    
    /**
     * 根据CVE ID查找受影响组件
     */
    @Query("SELECT ac FROM CveAffectedComponentEntity ac WHERE ac.cve.cveId = :cveId")
    List<CveAffectedComponentEntity> findByCveId(@Param("cveId") String cveId);
    
    /**
     * 根据产品名称查找受影响组件
     */
    List<CveAffectedComponentEntity> findByProduct(String product);
    
    /**
     * 根据产品名称（模糊匹配）查找受影响组件
     */
    @Query("SELECT ac FROM CveAffectedComponentEntity ac WHERE LOWER(ac.product) LIKE LOWER(CONCAT('%', :product, '%'))")
    List<CveAffectedComponentEntity> findByProductContaining(@Param("product") String product);
    
    /**
     * 根据供应商查找受影响组件
     */
    List<CveAffectedComponentEntity> findByVendor(String vendor);
    
    /**
     * 根据供应商和产品查找受影响组件
     */
    List<CveAffectedComponentEntity> findByVendorAndProduct(String vendor, String product);
    
    /**
     * 根据易受攻击状态查找组件
     */
    List<CveAffectedComponentEntity> findByVulnerable(Boolean vulnerable);
    
    /**
     * 根据CPE URI查找组件
     */
    List<CveAffectedComponentEntity> findByCpe23Uri(String cpe23Uri);
    
    /**
     * 根据CPE URI（模糊匹配）查找组件
     */
    @Query("SELECT ac FROM CveAffectedComponentEntity ac WHERE ac.cpe23Uri LIKE CONCAT('%', :cpePattern, '%')")
    List<CveAffectedComponentEntity> findByCpe23UriContaining(@Param("cpePattern") String cpePattern);
    
    /**
     * 查找指定产品的所有版本范围
     */
    @Query("SELECT DISTINCT ac.versionStartIncluding, ac.versionEndExcluding, ac.versionStartExcluding, ac.versionEndIncluding " +
           "FROM CveAffectedComponentEntity ac WHERE ac.product = :product AND ac.vulnerable = true")
    List<Object[]> findVersionRangesByProduct(@Param("product") String product);
    
    /**
     * 统计各产品的漏洞数量
     */
    @Query("SELECT ac.product, COUNT(DISTINCT ac.cve) FROM CveAffectedComponentEntity ac " +
           "WHERE ac.vulnerable = true GROUP BY ac.product ORDER BY COUNT(DISTINCT ac.cve) DESC")
    List<Object[]> countVulnerabilitiesByProduct();
    
    /**
     * 统计各供应商的漏洞数量
     */
    @Query("SELECT ac.vendor, COUNT(DISTINCT ac.cve) FROM CveAffectedComponentEntity ac " +
           "WHERE ac.vulnerable = true GROUP BY ac.vendor ORDER BY COUNT(DISTINCT ac.cve) DESC")
    List<Object[]> countVulnerabilitiesByVendor();
    
    /**
     * 查找影响特定版本的组件
     */
    @Query("SELECT ac FROM CveAffectedComponentEntity ac WHERE ac.product = :product " +
           "AND ac.vulnerable = true " +
           "AND (" +
           "  (ac.versionStartIncluding IS NULL OR :version >= ac.versionStartIncluding) " +
           "  AND (ac.versionEndExcluding IS NULL OR :version < ac.versionEndExcluding) " +
           "  AND (ac.versionStartExcluding IS NULL OR :version > ac.versionStartExcluding) " +
           "  AND (ac.versionEndIncluding IS NULL OR :version <= ac.versionEndIncluding)" +
           ")")
    List<CveAffectedComponentEntity> findAffectedByProductAndVersion(@Param("product") String product, @Param("version") String version);
    
    /**
     * 删除指定CVE的所有受影响组件
     */
    void deleteByCve(CveEntity cve);
    
    /**
     * 根据CVE ID删除受影响组件
     */
    @Query("DELETE FROM CveAffectedComponentEntity ac WHERE ac.cve.cveId = :cveId")
    void deleteByCveId(@Param("cveId") String cveId);
    
    /**
     * 查找没有版本信息的组件
     */
    @Query("SELECT ac FROM CveAffectedComponentEntity ac WHERE " +
           "ac.versionStartIncluding IS NULL AND ac.versionEndExcluding IS NULL " +
           "AND ac.versionStartExcluding IS NULL AND ac.versionEndIncluding IS NULL")
    List<CveAffectedComponentEntity> findComponentsWithoutVersionInfo();
    
    /**
     * 查找有版本范围冲突的组件
     */
    @Query("SELECT ac FROM CveAffectedComponentEntity ac WHERE " +
           "(ac.versionStartIncluding IS NOT NULL AND ac.versionStartExcluding IS NOT NULL) " +
           "OR (ac.versionEndIncluding IS NOT NULL AND ac.versionEndExcluding IS NOT NULL)")
    List<CveAffectedComponentEntity> findComponentsWithVersionConflicts();
}
