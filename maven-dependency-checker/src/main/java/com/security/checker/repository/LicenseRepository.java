package com.security.checker.repository;

import com.security.checker.entity.LicenseEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 许可证数据访问接口
 */
@Repository
public interface LicenseRepository extends JpaRepository<LicenseEntity, Long> {
    
    /**
     * 根据许可证ID查找
     */
    Optional<LicenseEntity> findByLicenseId(String licenseId);
    
    /**
     * 根据SPDX ID查找
     */
    Optional<LicenseEntity> findBySpdxId(String spdxId);
    
    /**
     * 检查许可证ID是否存在
     */
    boolean existsByLicenseId(String licenseId);
    
    /**
     * 检查SPDX ID是否存在
     */
    boolean existsBySpdxId(String spdxId);
    
    /**
     * 根据许可证名称查找
     */
    Optional<LicenseEntity> findByName(String name);
    
    /**
     * 根据许可证名称（模糊匹配）查找
     */
    @Query("SELECT l FROM LicenseEntity l WHERE LOWER(l.name) LIKE LOWER(CONCAT('%', :name, '%'))")
    List<LicenseEntity> findByNameContaining(@Param("name") String name);
    
    /**
     * 根据商业友好性查找许可证
     */
    List<LicenseEntity> findByCommercialFriendly(Boolean commercialFriendly);
    
    /**
     * 查找商业不友好的许可证
     */
    @Query("SELECT l FROM LicenseEntity l WHERE l.commercialFriendly = false")
    List<LicenseEntity> findCommercialUnfriendlyLicenses();
    
    /**
     * 根据Copyleft类型查找许可证
     */
    List<LicenseEntity> findByCopyleftType(String copyleftType);
    
    /**
     * 查找Copyleft许可证
     */
    @Query("SELECT l FROM LicenseEntity l WHERE l.copyleftType != 'NONE'")
    List<LicenseEntity> findCopyleftLicenses();
    
    /**
     * 查找强Copyleft许可证
     */
    @Query("SELECT l FROM LicenseEntity l WHERE l.copyleftType IN ('STRONG', 'NETWORK')")
    List<LicenseEntity> findStrongCopyleftLicenses();
    
    /**
     * 根据风险等级查找许可证
     */
    List<LicenseEntity> findByRiskLevel(String riskLevel);
    
    /**
     * 查找高风险许可证
     */
    @Query("SELECT l FROM LicenseEntity l WHERE l.riskLevel IN ('HIGH', 'CRITICAL')")
    List<LicenseEntity> findHighRiskLicenses();
    
    /**
     * 根据是否需要源码公开查找许可证
     */
    List<LicenseEntity> findByRequiresSourceDisclosure(Boolean requiresSourceDisclosure);
    
    /**
     * 根据是否允许商业使用查找许可证
     */
    List<LicenseEntity> findByAllowsCommercialUse(Boolean allowsCommercialUse);
    
    /**
     * 根据许可证类别查找
     */
    List<LicenseEntity> findByCategory(String category);
    
    /**
     * 根据数据源查找许可证
     */
    List<LicenseEntity> findBySource(String source);
    
    /**
     * 查找已弃用的许可证
     */
    List<LicenseEntity> findByDeprecated(Boolean deprecated);
    
    /**
     * 根据多个许可证ID批量查找
     */
    List<LicenseEntity> findByLicenseIdIn(List<String> licenseIds);
    
    /**
     * 根据多个SPDX ID批量查找
     */
    List<LicenseEntity> findBySpdxIdIn(List<String> spdxIds);
    
    /**
     * 统计各风险等级的许可证数量
     */
    @Query("SELECT l.riskLevel, COUNT(l) FROM LicenseEntity l GROUP BY l.riskLevel")
    List<Object[]> countByRiskLevel();
    
    /**
     * 统计各Copyleft类型的许可证数量
     */
    @Query("SELECT l.copyleftType, COUNT(l) FROM LicenseEntity l GROUP BY l.copyleftType")
    List<Object[]> countByCopyleftType();
    
    /**
     * 统计商业友好性分布
     */
    @Query("SELECT l.commercialFriendly, COUNT(l) FROM LicenseEntity l GROUP BY l.commercialFriendly")
    List<Object[]> countByCommercialFriendly();
    
    /**
     * 查找需要特定权限的许可证
     */
    @Query("SELECT l FROM LicenseEntity l WHERE " +
           "(:requiresSourceDisclosure IS NULL OR l.requiresSourceDisclosure = :requiresSourceDisclosure) " +
           "AND (:allowsCommercialUse IS NULL OR l.allowsCommercialUse = :allowsCommercialUse) " +
           "AND (:allowsModification IS NULL OR l.allowsModification = :allowsModification)")
    List<LicenseEntity> findByPermissions(@Param("requiresSourceDisclosure") Boolean requiresSourceDisclosure,
                                         @Param("allowsCommercialUse") Boolean allowsCommercialUse,
                                         @Param("allowsModification") Boolean allowsModification);
    
    /**
     * 根据许可证文本搜索
     */
    @Query("SELECT l FROM LicenseEntity l WHERE LOWER(l.text) LIKE LOWER(CONCAT('%', :keyword, '%'))")
    List<LicenseEntity> searchByText(@Param("keyword") String keyword);
    
    /**
     * 根据描述搜索许可证
     */
    @Query("SELECT l FROM LicenseEntity l WHERE LOWER(l.description) LIKE LOWER(CONCAT('%', :keyword, '%'))")
    List<LicenseEntity> searchByDescription(@Param("keyword") String keyword);
    
    /**
     * 查找与指定许可证兼容的许可证
     * 这是一个简化的兼容性检查，实际应用中需要更复杂的逻辑
     */
    @Query("SELECT l FROM LicenseEntity l WHERE l.commercialFriendly = true " +
           "AND l.copyleftType = 'NONE' AND l.riskLevel IN ('LOW', 'MEDIUM')")
    List<LicenseEntity> findCompatibleLicenses();
    
    /**
     * 查找可能有冲突的许可证组合
     */
    @Query("SELECT l FROM LicenseEntity l WHERE l.copyleftType IN ('STRONG', 'NETWORK') " +
           "OR l.commercialFriendly = false")
    List<LicenseEntity> findPotentiallyConflictingLicenses();
    
    /**
     * 根据URL查找许可证
     */
    Optional<LicenseEntity> findByUrl(String url);
    
    /**
     * 查找没有SPDX ID的许可证
     */
    @Query("SELECT l FROM LicenseEntity l WHERE l.spdxId IS NULL OR l.spdxId = ''")
    List<LicenseEntity> findLicensesWithoutSpdxId();
    
    /**
     * 查找重复的许可证（相同名称但不同ID）
     */
    @Query("SELECT l.name, COUNT(l) FROM LicenseEntity l GROUP BY l.name HAVING COUNT(l) > 1")
    List<Object[]> findDuplicateLicenseNames();
}
