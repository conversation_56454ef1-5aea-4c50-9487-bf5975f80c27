package com.security.checker.resolver;

import org.springframework.stereotype.Component;
import org.springframework.beans.factory.annotation.Value;
import org.apache.maven.model.Model;
import org.apache.maven.model.io.xpp3.MavenXpp3Reader;

// Maven Resolver API imports
import org.eclipse.aether.DefaultRepositorySystemSession;
import org.eclipse.aether.RepositorySystem;
import org.eclipse.aether.RepositorySystemSession;
import org.eclipse.aether.artifact.Artifact;
import org.eclipse.aether.artifact.DefaultArtifact;
import org.eclipse.aether.collection.CollectRequest;
import org.eclipse.aether.collection.DependencyCollectionException;
import org.eclipse.aether.graph.Dependency;
import org.eclipse.aether.graph.DependencyNode;
import org.eclipse.aether.repository.LocalRepository;
import org.eclipse.aether.repository.RemoteRepository;
import org.eclipse.aether.resolution.DependencyRequest;
import org.eclipse.aether.resolution.DependencyResolutionException;
import org.eclipse.aether.impl.DefaultServiceLocator;
import org.eclipse.aether.connector.basic.BasicRepositoryConnectorFactory;
import org.eclipse.aether.spi.connector.RepositoryConnectorFactory;
import org.eclipse.aether.spi.connector.transport.TransporterFactory;
import org.eclipse.aether.transport.file.FileTransporterFactory;
import org.eclipse.aether.transport.http.HttpTransporterFactory;
import org.eclipse.aether.util.graph.visitor.PreorderNodeListGenerator;

import java.io.File;
import java.io.FileReader;
import java.util.*;

/**
 * Maven依赖解析器
 * 
 * 功能：
 * 1. 解析pom.xml文件
 * 2. 提取直接依赖和传递依赖
 * 3. 构建完整的依赖树
 * 4. 支持多模块项目
 * 
 * <AUTHOR>
 */
@Component
public class MavenDependencyResolver {

    @Value("${security-checker.maven.local-repository:~/.m2/repository}")
    private String localRepository;

    @Value("${security-checker.maven.central-url:https://repo1.maven.org/maven2}")
    private String centralUrl;

    @Value("${security-checker.maven.timeout-seconds:30}")
    private int timeoutSeconds;

    private RepositorySystem repositorySystem;
    private RepositorySystemSession session;
    private List<RemoteRepository> repositories;

    /**
     * 初始化Maven Resolver
     */
    private void initializeResolver() {
        if (repositorySystem == null) {
            try {
                // 使用Apache Maven的工厂方法创建Repository System
                repositorySystem = org.apache.maven.repository.internal.MavenRepositorySystemUtils.newServiceLocator()
                    .addService(RepositoryConnectorFactory.class, BasicRepositoryConnectorFactory.class)
                    .addService(TransporterFactory.class, FileTransporterFactory.class)
                    .addService(TransporterFactory.class, HttpTransporterFactory.class)
                    .getService(RepositorySystem.class);

                if (repositorySystem == null) {
                    throw new RuntimeException("无法创建RepositorySystem - 可能缺少必要的Maven Resolver依赖");
                }

                // 创建Session
                DefaultRepositorySystemSession session = new DefaultRepositorySystemSession();

                // 设置本地仓库
                String localRepoPath = localRepository.startsWith("~")
                    ? System.getProperty("user.home") + localRepository.substring(1)
                    : localRepository;
                LocalRepository localRepo = new LocalRepository(localRepoPath);
                session.setLocalRepositoryManager(repositorySystem.newLocalRepositoryManager(session, localRepo));

                // 设置超时
                session.setConfigProperty("aether.connector.connectTimeout", timeoutSeconds * 1000);
                session.setConfigProperty("aether.connector.requestTimeout", timeoutSeconds * 1000);

                this.session = session;

                // 设置远程仓库
                this.repositories = Arrays.asList(
                    new RemoteRepository.Builder("central", "default", centralUrl).build()
                );

                System.out.println("✅ Maven Resolver初始化成功");

            } catch (Exception e) {
                System.err.println("❌ Maven Resolver初始化失败: " + e.getMessage());
                e.printStackTrace();
                throw new RuntimeException("Maven Resolver初始化失败", e);
            }
        }
    }

    /**
     * 解析Maven依赖
     *
     * @param pomFile pom.xml文件
     * @param includeTransitive 是否包含传递依赖
     * @return 依赖列表
     */
    public List<DependencyInfo> resolveDependencies(File pomFile, boolean includeTransitive) throws Exception {
        List<DependencyInfo> dependencies = new ArrayList<>();

        // 1. 解析pom.xml
        Model model = parsePomFile(pomFile);

        // 2. 提取直接依赖
        List<DependencyInfo> directDependencies = extractDirectDependencies(model);
        dependencies.addAll(directDependencies);

        // 3. 如果需要，解析传递依赖
        if (includeTransitive) {
            System.out.println("🔄 开始解析传递依赖...");
            // 初始化Resolver（仅在需要传递依赖时）
            try {
                initializeResolver();
                List<DependencyInfo> transitiveDependencies = resolveTransitiveDependencies(directDependencies);
                dependencies.addAll(transitiveDependencies);
                System.out.println("✅ 传递依赖解析完成，总共发现 " + dependencies.size() + " 个依赖（包含直接依赖）");
            } catch (Exception e) {
                System.out.println("⚠️  传递依赖解析失败，继续使用直接依赖: " + e.getMessage());
                e.printStackTrace();
            }
        } else {
            System.out.println("ℹ️  跳过传递依赖解析（仅分析直接依赖）");
        }

        // 4. 去重和排序
        dependencies = deduplicateAndSort(dependencies);

        return dependencies;
    }

    /**
     * 解析pom.xml文件
     */
    private Model parsePomFile(File pomFile) throws Exception {
        MavenXpp3Reader reader = new MavenXpp3Reader();
        try (FileReader fileReader = new FileReader(pomFile)) {
            return reader.read(fileReader);
        }
    }

    /**
     * 提取直接依赖
     */
    private List<DependencyInfo> extractDirectDependencies(Model model) {
        List<DependencyInfo> dependencies = new ArrayList<>();

        if (model.getDependencies() != null) {
            for (org.apache.maven.model.Dependency dep : model.getDependencies()) {
                // 跳过test scope的依赖
                if ("test".equals(dep.getScope())) {
                    continue;
                }

                DependencyInfo depInfo = new DependencyInfo();
                depInfo.setGroupId(dep.getGroupId());
                depInfo.setArtifactId(dep.getArtifactId());
                depInfo.setVersion(resolveVersion(dep.getVersion(), model));
                depInfo.setScope(dep.getScope() != null ? dep.getScope() : "compile");
                depInfo.setType(dep.getType() != null ? dep.getType() : "jar");
                depInfo.setDirect(true);

                // 提取许可证信息（通过已知的许可证映射）
                depInfo.setLicenses(getKnownLicensesForDependency(dep.getGroupId(), dep.getArtifactId()));

                dependencies.add(depInfo);
            }
        }

        return dependencies;
    }

    /**
     * 获取已知依赖的许可证信息
     * 这是一个简化的实现，在实际项目中应该从Maven Central或其他源获取
     */
    private List<String> getKnownLicensesForDependency(String groupId, String artifactId) {
        List<String> licenses = new ArrayList<>();
        String key = groupId + ":" + artifactId;

        // 常见依赖的许可证映射
        switch (key) {
            // Apache Commons
            case "org.apache.commons:commons-lang3":
            case "org.apache.commons:commons-text":
            case "org.apache.commons:commons-collections4":
                licenses.add("Apache-2.0");
                break;

            // Spring Framework
            case "org.springframework:spring-core":
            case "org.springframework:spring-context":
            case "org.springframework:spring-beans":
            case "org.springframework:spring-web":
            case "org.springframework:spring-webmvc":
                licenses.add("Apache-2.0");
                break;

            // Spring Boot
            case "org.springframework.boot:spring-boot":
            case "org.springframework.boot:spring-boot-starter":
            case "org.springframework.boot:spring-boot-starter-web":
            case "org.springframework.boot:spring-boot-starter-data-jpa":
                licenses.add("Apache-2.0");
                break;

            // Jackson
            case "com.fasterxml.jackson.core:jackson-core":
            case "com.fasterxml.jackson.core:jackson-databind":
            case "com.fasterxml.jackson.core:jackson-annotations":
                licenses.add("Apache-2.0");
                break;

            // Hibernate
            case "org.hibernate:hibernate-core":
            case "org.hibernate:hibernate-entitymanager":
                licenses.add("LGPL-2.1");
                break;

            // MySQL Connector
            case "mysql:mysql-connector-java":
                licenses.add("GPL-2.0"); // 注意：这是GPL许可证
                break;

            // H2 Database
            case "com.h2database:h2":
                licenses.add("MPL-2.0");
                break;

            // JUnit
            case "junit:junit":
            case "org.junit.jupiter:junit-jupiter":
                licenses.add("EPL-2.0");
                break;

            // Logback
            case "ch.qos.logback:logback-classic":
            case "ch.qos.logback:logback-core":
                licenses.add("EPL-1.0");
                break;

            // SLF4J
            case "org.slf4j:slf4j-api":
                licenses.add("MIT");
                break;

            // Thymeleaf
            case "org.thymeleaf:thymeleaf":
            case "org.thymeleaf:thymeleaf-spring5":
                licenses.add("Apache-2.0");
                break;

            // Picocli
            case "info.picocli:picocli":
                licenses.add("Apache-2.0");
                break;

            // Maven Resolver
            case "org.apache.maven.resolver:maven-resolver-api":
            case "org.apache.maven.resolver:maven-resolver-impl":
            case "org.apache.maven.resolver:maven-resolver-connector-basic":
            case "org.apache.maven.resolver:maven-resolver-transport-file":
            case "org.apache.maven.resolver:maven-resolver-transport-http":
                licenses.add("Apache-2.0");
                break;

            default:
                // 对于未知的依赖，尝试根据groupId推断
                if (groupId.startsWith("org.apache.")) {
                    licenses.add("Apache-2.0");
                } else if (groupId.startsWith("org.springframework.")) {
                    licenses.add("Apache-2.0");
                } else if (groupId.startsWith("com.fasterxml.jackson.")) {
                    licenses.add("Apache-2.0");
                }
                // 如果无法推断，返回空列表
                break;
        }

        return licenses;
    }

    /**
     * 解析版本号（处理属性引用）
     */
    private String resolveVersion(String version, Model model) {
        if (version == null) {
            return "UNKNOWN";
        }
        
        // 处理属性引用，如 ${spring.version}
        if (version.startsWith("${") && version.endsWith("}")) {
            String propertyName = version.substring(2, version.length() - 1);
            
            // 从properties中查找
            if (model.getProperties() != null) {
                String propertyValue = model.getProperties().getProperty(propertyName);
                if (propertyValue != null) {
                    return propertyValue;
                }
            }
            
            // 从parent中查找
            if (model.getParent() != null) {
                // 这里简化处理，实际应该递归解析parent pom
                return "INHERITED";
            }
        }
        
        return version;
    }

    /**
     * 解析传递依赖
     * 使用Maven Resolver API进行完整的传递依赖解析
     */
    private List<DependencyInfo> resolveTransitiveDependencies(List<DependencyInfo> directDependencies) {
        List<DependencyInfo> transitiveDeps = new ArrayList<>();

        try {
            if (repositorySystem == null || session == null) {
                System.out.println("⚠️  Maven Resolver未正确初始化，跳过传递依赖解析");
                return transitiveDeps;
            }

            // 创建依赖收集请求
            CollectRequest collectRequest = new CollectRequest();

            // 添加直接依赖
            for (DependencyInfo depInfo : directDependencies) {
                Artifact artifact = new DefaultArtifact(
                    depInfo.getGroupId(),
                    depInfo.getArtifactId(),
                    depInfo.getType(),
                    depInfo.getVersion()
                );
                Dependency dependency = new Dependency(artifact, depInfo.getScope());
                collectRequest.addDependency(dependency);
            }

            // 设置仓库
            collectRequest.setRepositories(repositories);

            // 收集依赖
            DependencyNode rootNode = repositorySystem.collectDependencies(session, collectRequest).getRoot();

            // 遍历依赖树，提取传递依赖
            PreorderNodeListGenerator nlg = new PreorderNodeListGenerator();
            rootNode.accept(nlg);

            for (DependencyNode node : nlg.getNodes()) {
                if (node.getDependency() != null) {
                    Artifact artifact = node.getDependency().getArtifact();

                    // 检查是否已经在直接依赖中
                    boolean isDirect = directDependencies.stream()
                        .anyMatch(dep -> dep.getGroupId().equals(artifact.getGroupId())
                                    && dep.getArtifactId().equals(artifact.getArtifactId()));

                    if (!isDirect) {
                        DependencyInfo transitiveDep = new DependencyInfo();
                        transitiveDep.setGroupId(artifact.getGroupId());
                        transitiveDep.setArtifactId(artifact.getArtifactId());
                        transitiveDep.setVersion(artifact.getVersion());
                        transitiveDep.setScope(node.getDependency().getScope());
                        transitiveDep.setType(artifact.getExtension());
                        transitiveDep.setDirect(false);

                        // 为传递依赖也添加许可证信息
                        transitiveDep.setLicenses(getKnownLicensesForDependency(artifact.getGroupId(), artifact.getArtifactId()));

                        transitiveDeps.add(transitiveDep);
                    }
                }
            }

            System.out.println("✅ 传递依赖解析完成，发现 " + transitiveDeps.size() + " 个传递依赖");

        } catch (Exception e) {
            System.err.println("❌ 传递依赖解析失败: " + e.getMessage());
            // 在离线模式下，传递依赖解析可能失败，但不应该阻止整个流程
            System.out.println("⚠️  继续使用仅直接依赖的分析...");
        }

        return transitiveDeps;
    }

    /**
     * 去重和排序
     */
    private List<DependencyInfo> deduplicateAndSort(List<DependencyInfo> dependencies) {
        // 使用LinkedHashSet保持插入顺序并去重
        Set<DependencyInfo> uniqueDeps = new LinkedHashSet<>(dependencies);
        
        List<DependencyInfo> result = new ArrayList<>(uniqueDeps);
        
        // 按groupId:artifactId排序
        result.sort((d1, d2) -> {
            String key1 = d1.getGroupId() + ":" + d1.getArtifactId();
            String key2 = d2.getGroupId() + ":" + d2.getArtifactId();
            return key1.compareTo(key2);
        });
        
        return result;
    }

    /**
     * 构建依赖树结构
     *
     * @param pomFile pom.xml文件
     * @return 依赖树根节点
     */
    public DependencyTreeNode buildDependencyTree(File pomFile) throws Exception {
        initializeResolver();

        Model model = parsePomFile(pomFile);
        List<DependencyInfo> directDependencies = extractDirectDependencies(model);

        DependencyTreeNode root = new DependencyTreeNode();
        root.setGroupId(model.getGroupId() != null ? model.getGroupId() : model.getParent().getGroupId());
        root.setArtifactId(model.getArtifactId());
        root.setVersion(model.getVersion() != null ? model.getVersion() : model.getParent().getVersion());
        root.setScope("compile");
        root.setType("pom");
        root.setDirect(true);

        try {
            // 创建依赖收集请求
            CollectRequest collectRequest = new CollectRequest();

            for (DependencyInfo depInfo : directDependencies) {
                Artifact artifact = new DefaultArtifact(
                    depInfo.getGroupId(),
                    depInfo.getArtifactId(),
                    depInfo.getType(),
                    depInfo.getVersion()
                );
                Dependency dependency = new Dependency(artifact, depInfo.getScope());
                collectRequest.addDependency(dependency);
            }

            collectRequest.setRepositories(repositories);

            // 收集依赖树
            DependencyNode rootNode = repositorySystem.collectDependencies(session, collectRequest).getRoot();

            // 转换为我们的树结构
            convertToTreeNode(rootNode, root);

        } catch (DependencyCollectionException e) {
            System.err.println("❌ 依赖树构建失败: " + e.getMessage());
        }

        return root;
    }

    /**
     * 转换Maven依赖节点为我们的树节点
     */
    private void convertToTreeNode(DependencyNode mavenNode, DependencyTreeNode treeNode) {
        for (DependencyNode child : mavenNode.getChildren()) {
            if (child.getDependency() != null) {
                Artifact artifact = child.getDependency().getArtifact();

                DependencyTreeNode childNode = new DependencyTreeNode();
                childNode.setGroupId(artifact.getGroupId());
                childNode.setArtifactId(artifact.getArtifactId());
                childNode.setVersion(artifact.getVersion());
                childNode.setScope(child.getDependency().getScope());
                childNode.setType(artifact.getExtension());
                childNode.setDirect(treeNode.isDirect() && mavenNode.getChildren().size() > 0);

                treeNode.addChild(childNode);

                // 递归处理子节点
                convertToTreeNode(child, childNode);
            }
        }
    }

    /**
     * 检查依赖冲突
     *
     * @param dependencies 依赖列表
     * @return 冲突的依赖组
     */
    public List<DependencyConflict> checkDependencyConflicts(List<DependencyInfo> dependencies) {
        List<DependencyConflict> conflicts = new ArrayList<>();
        Map<String, List<DependencyInfo>> groupedDeps = new HashMap<>();

        // 按groupId:artifactId分组
        for (DependencyInfo dep : dependencies) {
            String key = dep.getGroupId() + ":" + dep.getArtifactId();
            groupedDeps.computeIfAbsent(key, k -> new ArrayList<>()).add(dep);
        }

        // 检查版本冲突
        for (Map.Entry<String, List<DependencyInfo>> entry : groupedDeps.entrySet()) {
            List<DependencyInfo> versions = entry.getValue();
            if (versions.size() > 1) {
                // 有多个版本，可能存在冲突
                Set<String> uniqueVersions = new HashSet<>();
                for (DependencyInfo dep : versions) {
                    uniqueVersions.add(dep.getVersion());
                }

                if (uniqueVersions.size() > 1) {
                    DependencyConflict conflict = new DependencyConflict();
                    conflict.setGroupId(versions.get(0).getGroupId());
                    conflict.setArtifactId(versions.get(0).getArtifactId());
                    conflict.setConflictingVersions(new ArrayList<>(uniqueVersions));
                    conflict.setDependencies(versions);

                    conflicts.add(conflict);
                }
            }
        }

        return conflicts;
    }

    /**
     * 依赖信息类
     */
    public static class DependencyInfo {
        private String groupId;
        private String artifactId;
        private String version;
        private String scope;
        private String type;
        private boolean direct;
        private List<String> licenses = new ArrayList<>();
        private List<String> cveIds = new ArrayList<>();

        // Getters and Setters
        public String getGroupId() { return groupId; }
        public void setGroupId(String groupId) { this.groupId = groupId; }

        public String getArtifactId() { return artifactId; }
        public void setArtifactId(String artifactId) { this.artifactId = artifactId; }

        public String getVersion() { return version; }
        public void setVersion(String version) { this.version = version; }

        public String getScope() { return scope; }
        public void setScope(String scope) { this.scope = scope; }

        public String getType() { return type; }
        public void setType(String type) { this.type = type; }

        public boolean isDirect() { return direct; }
        public void setDirect(boolean direct) { this.direct = direct; }

        public List<String> getLicenses() { return licenses; }
        public void setLicenses(List<String> licenses) { this.licenses = licenses; }

        public List<String> getCveIds() { return cveIds; }
        public void setCveIds(List<String> cveIds) { this.cveIds = cveIds; }

        public String getCoordinate() {
            return groupId + ":" + artifactId + ":" + version;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (!(o instanceof DependencyInfo)) return false;
            DependencyInfo that = (DependencyInfo) o;
            return Objects.equals(groupId, that.groupId) &&
                   Objects.equals(artifactId, that.artifactId) &&
                   Objects.equals(version, that.version);
        }

        @Override
        public int hashCode() {
            return Objects.hash(groupId, artifactId, version);
        }

        @Override
        public String toString() {
            return getCoordinate() + " (" + scope + ")";
        }
    }

    /**
     * 依赖树节点类
     */
    public static class DependencyTreeNode extends DependencyInfo {
        private List<DependencyTreeNode> children = new ArrayList<>();
        private DependencyTreeNode parent;
        private int depth = 0;

        public List<DependencyTreeNode> getChildren() { return children; }
        public void setChildren(List<DependencyTreeNode> children) { this.children = children; }

        public DependencyTreeNode getParent() { return parent; }
        public void setParent(DependencyTreeNode parent) { this.parent = parent; }

        public int getDepth() { return depth; }
        public void setDepth(int depth) { this.depth = depth; }

        public void addChild(DependencyTreeNode child) {
            child.setParent(this);
            child.setDepth(this.depth + 1);
            this.children.add(child);
        }

        public boolean hasChildren() {
            return !children.isEmpty();
        }

        /**
         * 获取所有后代节点（递归）
         */
        public List<DependencyTreeNode> getAllDescendants() {
            List<DependencyTreeNode> descendants = new ArrayList<>();
            for (DependencyTreeNode child : children) {
                descendants.add(child);
                descendants.addAll(child.getAllDescendants());
            }
            return descendants;
        }

        /**
         * 打印依赖树
         */
        public void printTree() {
            printTree("", true);
        }

        private void printTree(String prefix, boolean isLast) {
            System.out.println(prefix + (isLast ? "└── " : "├── ") + getCoordinate());

            for (int i = 0; i < children.size(); i++) {
                boolean childIsLast = (i == children.size() - 1);
                String childPrefix = prefix + (isLast ? "    " : "│   ");
                children.get(i).printTree(childPrefix, childIsLast);
            }
        }
    }

    /**
     * 依赖冲突类
     */
    public static class DependencyConflict {
        private String groupId;
        private String artifactId;
        private List<String> conflictingVersions;
        private List<DependencyInfo> dependencies;

        public String getGroupId() { return groupId; }
        public void setGroupId(String groupId) { this.groupId = groupId; }

        public String getArtifactId() { return artifactId; }
        public void setArtifactId(String artifactId) { this.artifactId = artifactId; }

        public List<String> getConflictingVersions() { return conflictingVersions; }
        public void setConflictingVersions(List<String> conflictingVersions) { this.conflictingVersions = conflictingVersions; }

        public List<DependencyInfo> getDependencies() { return dependencies; }
        public void setDependencies(List<DependencyInfo> dependencies) { this.dependencies = dependencies; }

        public String getCoordinate() {
            return groupId + ":" + artifactId;
        }

        @Override
        public String toString() {
            return getCoordinate() + " has conflicting versions: " + conflictingVersions;
        }
    }
}
