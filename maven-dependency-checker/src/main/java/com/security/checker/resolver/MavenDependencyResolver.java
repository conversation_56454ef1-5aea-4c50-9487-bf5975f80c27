package com.security.checker.resolver;

import org.springframework.stereotype.Component;
import org.springframework.beans.factory.annotation.Value;
import org.apache.maven.model.Model;
import org.apache.maven.model.io.xpp3.MavenXpp3Reader;

import java.io.File;
import java.io.FileReader;
import java.util.*;

/**
 * Maven依赖解析器
 * 
 * 功能：
 * 1. 解析pom.xml文件
 * 2. 提取直接依赖和传递依赖
 * 3. 构建完整的依赖树
 * 4. 支持多模块项目
 * 
 * <AUTHOR>
 */
@Component
public class MavenDependencyResolver {

    @Value("${security-checker.maven.local-repository:~/.m2/repository}")
    private String localRepository;

    @Value("${security-checker.maven.central-url:https://repo1.maven.org/maven2}")
    private String centralUrl;

    @Value("${security-checker.maven.timeout-seconds:30}")
    private int timeoutSeconds;

    /**
     * 解析Maven依赖
     * 
     * @param pomFile pom.xml文件
     * @param includeTransitive 是否包含传递依赖
     * @return 依赖列表
     */
    public List<DependencyInfo> resolveDependencies(File pomFile, boolean includeTransitive) throws Exception {
        List<DependencyInfo> dependencies = new ArrayList<>();
        
        // 1. 解析pom.xml
        Model model = parsePomFile(pomFile);
        
        // 2. 提取直接依赖
        List<DependencyInfo> directDependencies = extractDirectDependencies(model);
        dependencies.addAll(directDependencies);
        
        // 3. 如果需要，解析传递依赖
        if (includeTransitive) {
            List<DependencyInfo> transitiveDependencies = resolveTransitiveDependencies(directDependencies);
            dependencies.addAll(transitiveDependencies);
        }
        
        // 4. 去重和排序
        dependencies = deduplicateAndSort(dependencies);
        
        return dependencies;
    }

    /**
     * 解析pom.xml文件
     */
    private Model parsePomFile(File pomFile) throws Exception {
        MavenXpp3Reader reader = new MavenXpp3Reader();
        try (FileReader fileReader = new FileReader(pomFile)) {
            return reader.read(fileReader);
        }
    }

    /**
     * 提取直接依赖
     */
    private List<DependencyInfo> extractDirectDependencies(Model model) {
        List<DependencyInfo> dependencies = new ArrayList<>();
        
        if (model.getDependencies() != null) {
            for (org.apache.maven.model.Dependency dep : model.getDependencies()) {
                // 跳过test scope的依赖
                if ("test".equals(dep.getScope())) {
                    continue;
                }
                
                DependencyInfo depInfo = new DependencyInfo();
                depInfo.setGroupId(dep.getGroupId());
                depInfo.setArtifactId(dep.getArtifactId());
                depInfo.setVersion(resolveVersion(dep.getVersion(), model));
                depInfo.setScope(dep.getScope() != null ? dep.getScope() : "compile");
                depInfo.setType(dep.getType() != null ? dep.getType() : "jar");
                depInfo.setDirect(true);
                
                dependencies.add(depInfo);
            }
        }
        
        return dependencies;
    }

    /**
     * 解析版本号（处理属性引用）
     */
    private String resolveVersion(String version, Model model) {
        if (version == null) {
            return "UNKNOWN";
        }
        
        // 处理属性引用，如 ${spring.version}
        if (version.startsWith("${") && version.endsWith("}")) {
            String propertyName = version.substring(2, version.length() - 1);
            
            // 从properties中查找
            if (model.getProperties() != null) {
                String propertyValue = model.getProperties().getProperty(propertyName);
                if (propertyValue != null) {
                    return propertyValue;
                }
            }
            
            // 从parent中查找
            if (model.getParent() != null) {
                // 这里简化处理，实际应该递归解析parent pom
                return "INHERITED";
            }
        }
        
        return version;
    }

    /**
     * 解析传递依赖（简化实现）
     * 注意：完整的传递依赖解析需要Maven Resolver API
     */
    private List<DependencyInfo> resolveTransitiveDependencies(List<DependencyInfo> directDependencies) {
        List<DependencyInfo> transitiveDeps = new ArrayList<>();
        
        // TODO: 实现完整的传递依赖解析
        // 这里提供一个简化的框架，实际实现需要：
        // 1. 使用Maven Resolver API
        // 2. 递归解析每个依赖的pom文件
        // 3. 处理版本冲突和依赖排除
        
        System.out.println("⚠️  传递依赖解析功能开发中...");
        
        return transitiveDeps;
    }

    /**
     * 去重和排序
     */
    private List<DependencyInfo> deduplicateAndSort(List<DependencyInfo> dependencies) {
        // 使用LinkedHashSet保持插入顺序并去重
        Set<DependencyInfo> uniqueDeps = new LinkedHashSet<>(dependencies);
        
        List<DependencyInfo> result = new ArrayList<>(uniqueDeps);
        
        // 按groupId:artifactId排序
        result.sort((d1, d2) -> {
            String key1 = d1.getGroupId() + ":" + d1.getArtifactId();
            String key2 = d2.getGroupId() + ":" + d2.getArtifactId();
            return key1.compareTo(key2);
        });
        
        return result;
    }

    /**
     * 依赖信息类
     */
    public static class DependencyInfo {
        private String groupId;
        private String artifactId;
        private String version;
        private String scope;
        private String type;
        private boolean direct;
        private List<String> licenses = new ArrayList<>();
        private List<String> cveIds = new ArrayList<>();

        // Getters and Setters
        public String getGroupId() { return groupId; }
        public void setGroupId(String groupId) { this.groupId = groupId; }

        public String getArtifactId() { return artifactId; }
        public void setArtifactId(String artifactId) { this.artifactId = artifactId; }

        public String getVersion() { return version; }
        public void setVersion(String version) { this.version = version; }

        public String getScope() { return scope; }
        public void setScope(String scope) { this.scope = scope; }

        public String getType() { return type; }
        public void setType(String type) { this.type = type; }

        public boolean isDirect() { return direct; }
        public void setDirect(boolean direct) { this.direct = direct; }

        public List<String> getLicenses() { return licenses; }
        public void setLicenses(List<String> licenses) { this.licenses = licenses; }

        public List<String> getCveIds() { return cveIds; }
        public void setCveIds(List<String> cveIds) { this.cveIds = cveIds; }

        public String getCoordinate() {
            return groupId + ":" + artifactId + ":" + version;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (!(o instanceof DependencyInfo)) return false;
            DependencyInfo that = (DependencyInfo) o;
            return Objects.equals(groupId, that.groupId) &&
                   Objects.equals(artifactId, that.artifactId) &&
                   Objects.equals(version, that.version);
        }

        @Override
        public int hashCode() {
            return Objects.hash(groupId, artifactId, version);
        }

        @Override
        public String toString() {
            return getCoordinate() + " (" + scope + ")";
        }
    }
}
