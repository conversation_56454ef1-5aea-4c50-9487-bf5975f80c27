package com.security.checker.scanner;

import com.security.checker.entity.LicenseEntity;
import com.security.checker.resolver.MavenDependencyResolver.DependencyInfo;

/**
 * 许可证信息类
 * 封装依赖项的许可证信息和风险评估
 * 
 * <AUTHOR>
 */
public class LicenseInfo {
    
    private DependencyInfo dependency;
    private String licenseName;
    private String normalizedLicenseName;
    private String spdxId;
    private String licenseUrl;
    private boolean restricted;
    private String riskLevel;
    private String copyleftType;
    private boolean commercialFriendly;
    private boolean requiresSourceDisclosure;
    private String detectionMethod;
    private String confidence;
    private String declaredLicense;
    private String comments;
    
    // 构造函数
    public LicenseInfo(DependencyInfo dependency) {
        this.dependency = dependency;
        this.restricted = false;
        this.riskLevel = "UNKNOWN";
        this.copyleftType = "NONE";
        this.commercialFriendly = true;
        this.requiresSourceDisclosure = false;
        this.confidence = "LOW";
    }
    
    public LicenseInfo(LicenseEntity license, DependencyInfo dependency) {
        this(dependency);
        this.licenseName = license.getName();
        this.normalizedLicenseName = license.getLicenseId();
        this.spdxId = license.getSpdxId();
        this.licenseUrl = license.getUrl();
        this.restricted = !license.getCommercialFriendly();
        this.riskLevel = license.getRiskLevel();
        this.copyleftType = license.getCopyleftType();
        this.commercialFriendly = license.getCommercialFriendly();
        this.requiresSourceDisclosure = license.getRequiresSourceDisclosure();
        this.confidence = "HIGH";
    }
    
    // Getters and Setters
    public DependencyInfo getDependency() { return dependency; }
    public void setDependency(DependencyInfo dependency) { this.dependency = dependency; }
    
    public String getLicenseName() { return licenseName; }
    public void setLicenseName(String licenseName) { this.licenseName = licenseName; }
    
    public String getNormalizedLicenseName() { return normalizedLicenseName; }
    public void setNormalizedLicenseName(String normalizedLicenseName) { this.normalizedLicenseName = normalizedLicenseName; }
    
    public String getSpdxId() { return spdxId; }
    public void setSpdxId(String spdxId) { this.spdxId = spdxId; }
    
    public String getLicenseUrl() { return licenseUrl; }
    public void setLicenseUrl(String licenseUrl) { this.licenseUrl = licenseUrl; }
    
    public boolean isRestricted() { return restricted; }
    public void setRestricted(boolean restricted) { this.restricted = restricted; }
    
    public String getRiskLevel() { return riskLevel; }
    public void setRiskLevel(String riskLevel) { this.riskLevel = riskLevel; }
    
    public String getCopyleftType() { return copyleftType; }
    public void setCopyleftType(String copyleftType) { this.copyleftType = copyleftType; }
    
    public boolean isCommercialFriendly() { return commercialFriendly; }
    public void setCommercialFriendly(boolean commercialFriendly) { this.commercialFriendly = commercialFriendly; }
    
    public boolean isRequiresSourceDisclosure() { return requiresSourceDisclosure; }
    public void setRequiresSourceDisclosure(boolean requiresSourceDisclosure) { this.requiresSourceDisclosure = requiresSourceDisclosure; }
    
    public String getDetectionMethod() { return detectionMethod; }
    public void setDetectionMethod(String detectionMethod) { this.detectionMethod = detectionMethod; }
    
    public String getConfidence() { return confidence; }
    public void setConfidence(String confidence) { this.confidence = confidence; }
    
    public String getDeclaredLicense() { return declaredLicense; }
    public void setDeclaredLicense(String declaredLicense) { this.declaredLicense = declaredLicense; }
    
    public String getComments() { return comments; }
    public void setComments(String comments) { this.comments = comments; }
    
    // 实用方法
    public boolean isCopyleft() {
        return copyleftType != null && !"NONE".equals(copyleftType.toUpperCase());
    }
    
    public boolean isStrongCopyleft() {
        return "STRONG".equals(copyleftType) || "NETWORK".equals(copyleftType);
    }
    
    public boolean isHighRisk() {
        return "HIGH".equals(riskLevel) || "CRITICAL".equals(riskLevel);
    }
    
    public String getDisplayName() {
        if (licenseName != null && !licenseName.trim().isEmpty()) {
            return licenseName;
        } else if (spdxId != null && !spdxId.trim().isEmpty()) {
            return spdxId;
        } else if (normalizedLicenseName != null && !normalizedLicenseName.trim().isEmpty()) {
            return normalizedLicenseName;
        } else {
            return "Unknown License";
        }
    }
    
    @Override
    public String toString() {
        return "LicenseInfo{" +
                "dependency=" + (dependency != null ? dependency.getCoordinate() : "null") +
                ", licenseName='" + licenseName + '\'' +
                ", restricted=" + restricted +
                ", riskLevel='" + riskLevel + '\'' +
                ", commercialFriendly=" + commercialFriendly +
                '}';
    }
}
