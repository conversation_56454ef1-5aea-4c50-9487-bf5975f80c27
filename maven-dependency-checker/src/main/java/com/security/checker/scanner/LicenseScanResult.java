package com.security.checker.scanner;

import com.security.checker.resolver.MavenDependencyResolver.DependencyInfo;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 许可证扫描结果类
 * 存储许可证合规性扫描的结果和统计信息
 * 
 * <AUTHOR>
 */
public class LicenseScanResult {
    
    private int totalDependencies = 0;
    private int restrictedLicenseCount = 0;
    private int unknownLicenseCount = 0;
    private int compatibleLicenseCount = 0;
    
    private List<LicenseInfo> allLicenseInfos = new ArrayList<>();
    private Map<DependencyInfo, List<LicenseInfo>> dependencyLicenseMap = new HashMap<>();
    
    // 添加许可证信息
    public void addLicenseInfo(LicenseInfo licenseInfo) {
        allLicenseInfos.add(licenseInfo);
        
        DependencyInfo dependency = licenseInfo.getDependency();
        dependencyLicenseMap.computeIfAbsent(dependency, k -> new ArrayList<>()).add(licenseInfo);
        
        // 更新统计
        if (licenseInfo.isRestricted()) {
            restrictedLicenseCount++;
        } else if (licenseInfo.isCommercialFriendly()) {
            compatibleLicenseCount++;
        }
    }
    
    // 增加未知许可证计数
    public void incrementUnknownLicenseCount() {
        unknownLicenseCount++;
    }
    
    // 获取限制性许可证信息
    public List<LicenseInfo> getRestrictedLicenses() {
        return allLicenseInfos.stream()
                .filter(LicenseInfo::isRestricted)
                .collect(Collectors.toList());
    }
    
    // 获取高风险许可证信息
    public List<LicenseInfo> getHighRiskLicenses() {
        return allLicenseInfos.stream()
                .filter(LicenseInfo::isHighRisk)
                .collect(Collectors.toList());
    }
    
    // 获取Copyleft许可证信息
    public List<LicenseInfo> getCopyleftLicenses() {
        return allLicenseInfos.stream()
                .filter(LicenseInfo::isCopyleft)
                .collect(Collectors.toList());
    }
    
    // 获取特定依赖的许可证信息
    public List<LicenseInfo> getLicensesForDependency(DependencyInfo dependency) {
        return dependencyLicenseMap.getOrDefault(dependency, new ArrayList<>());
    }
    
    // 获取风险等级统计
    public Map<String, Integer> getRiskLevelStatistics() {
        Map<String, Integer> stats = new HashMap<>();
        for (LicenseInfo licenseInfo : allLicenseInfos) {
            String riskLevel = licenseInfo.getRiskLevel();
            stats.put(riskLevel, stats.getOrDefault(riskLevel, 0) + 1);
        }
        return stats;
    }
    
    // 获取许可证类型统计
    public Map<String, Integer> getLicenseTypeStatistics() {
        Map<String, Integer> stats = new HashMap<>();
        for (LicenseInfo licenseInfo : allLicenseInfos) {
            String licenseName = licenseInfo.getDisplayName();
            stats.put(licenseName, stats.getOrDefault(licenseName, 0) + 1);
        }
        return stats;
    }
    
    // 获取Copyleft类型统计
    public Map<String, Integer> getCopyleftTypeStatistics() {
        Map<String, Integer> stats = new HashMap<>();
        for (LicenseInfo licenseInfo : allLicenseInfos) {
            String copyleftType = licenseInfo.getCopyleftType();
            if (copyleftType != null && !"NONE".equals(copyleftType)) {
                stats.put(copyleftType, stats.getOrDefault(copyleftType, 0) + 1);
            }
        }
        return stats;
    }
    
    // 检查是否有限制性许可证
    public boolean hasRestrictedLicenses() {
        return restrictedLicenseCount > 0;
    }
    
    // 检查是否有高风险许可证
    public boolean hasHighRiskLicenses() {
        return allLicenseInfos.stream().anyMatch(LicenseInfo::isHighRisk);
    }
    
    // 检查是否有Copyleft许可证
    public boolean hasCopyleftLicenses() {
        return allLicenseInfos.stream().anyMatch(LicenseInfo::isCopyleft);
    }
    
    // Getters and Setters
    public int getTotalDependencies() { return totalDependencies; }
    public void setTotalDependencies(int totalDependencies) { this.totalDependencies = totalDependencies; }
    
    public int getRestrictedLicenseCount() { return restrictedLicenseCount; }
    public void setRestrictedLicenseCount(int restrictedLicenseCount) { this.restrictedLicenseCount = restrictedLicenseCount; }
    
    public int getUnknownLicenseCount() { return unknownLicenseCount; }
    public void setUnknownLicenseCount(int unknownLicenseCount) { this.unknownLicenseCount = unknownLicenseCount; }
    
    public int getCompatibleLicenseCount() { return compatibleLicenseCount; }
    public void setCompatibleLicenseCount(int compatibleLicenseCount) { this.compatibleLicenseCount = compatibleLicenseCount; }
    
    public List<LicenseInfo> getAllLicenseInfos() { return new ArrayList<>(allLicenseInfos); }
    
    public Map<DependencyInfo, List<LicenseInfo>> getDependencyLicenseMap() { 
        return new HashMap<>(dependencyLicenseMap); 
    }
    
    @Override
    public String toString() {
        return "LicenseScanResult{" +
                "totalDependencies=" + totalDependencies +
                ", restrictedLicenseCount=" + restrictedLicenseCount +
                ", unknownLicenseCount=" + unknownLicenseCount +
                ", compatibleLicenseCount=" + compatibleLicenseCount +
                ", totalLicenseInfos=" + allLicenseInfos.size() +
                '}';
    }
}
