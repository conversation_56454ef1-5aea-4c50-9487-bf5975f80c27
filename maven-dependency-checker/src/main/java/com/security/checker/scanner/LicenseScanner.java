package com.security.checker.scanner;

import org.springframework.stereotype.Component;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import com.security.checker.resolver.MavenDependencyResolver.DependencyInfo;
import com.security.checker.repository.LicenseRepository;
import com.security.checker.repository.ComponentLicenseRepository;
import com.security.checker.entity.LicenseEntity;
import com.security.checker.entity.ComponentLicenseEntity;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 许可证合规性扫描器
 *
 * 功能：
 * 1. 检查依赖的许可证类型
 * 2. 识别商业不友好的许可证
 * 3. 许可证兼容性分析
 * 4. 生成合规性报告
 *
 * <AUTHOR>
 */
@Component
public class LicenseScanner {

    @Autowired
    private LicenseRepository licenseRepository;

    @Autowired
    private ComponentLicenseRepository componentLicenseRepository;

    @Value("${security-checker.license.enable-fuzzy-matching:true}")
    private boolean enableFuzzyMatching;

    @Value("${security-checker.license.strict-mode:false}")
    private boolean strictMode;

    // 商业不友好的许可证列表（用于快速检查）
    private static final Set<String> RESTRICTED_LICENSES = new HashSet<>(Arrays.asList(
        // 强制开源类（Copyleft）
        "GPL-2.0", "GPL-2.0-only", "GPL-2.0-or-later",
        "GPL-3.0", "GPL-3.0-only", "GPL-3.0-or-later",
        "AGPL-3.0", "AGPL-3.0-only", "AGPL-3.0-or-later",
        "LGPL-2.1", "LGPL-2.1-only", "LGPL-2.1-or-later",
        "LGPL-3.0", "LGPL-3.0-only", "LGPL-3.0-or-later",
        "EUPL-1.2", "OSL-3.0",

        // 商业限制类
        "SSPL-1.0", "BUSL-1.1", "Commons Clause",
        "CPAL-1.0", "CDDL-1.0", "CDDL-1.1",

        // 其他风险许可证
        "EPL-1.0", "EPL-2.0", "MPL-2.0"
    ));

    // 许可证名称别名映射
    private static final Map<String, String> LICENSE_ALIASES = new HashMap<>();
    static {
        // GPL 别名
        LICENSE_ALIASES.put("GNU GENERAL PUBLIC LICENSE V2", "GPL-2.0");
        LICENSE_ALIASES.put("GNU GENERAL PUBLIC LICENSE VERSION 2", "GPL-2.0");
        LICENSE_ALIASES.put("GNU GENERAL PUBLIC LICENSE V3", "GPL-3.0");
        LICENSE_ALIASES.put("GNU GENERAL PUBLIC LICENSE VERSION 3", "GPL-3.0");

        // AGPL 别名
        LICENSE_ALIASES.put("GNU AFFERO GENERAL PUBLIC LICENSE V3", "AGPL-3.0");
        LICENSE_ALIASES.put("GNU AFFERO GENERAL PUBLIC LICENSE VERSION 3", "AGPL-3.0");

        // LGPL 别名
        LICENSE_ALIASES.put("GNU LESSER GENERAL PUBLIC LICENSE V2.1", "LGPL-2.1");
        LICENSE_ALIASES.put("GNU LESSER GENERAL PUBLIC LICENSE VERSION 2.1", "LGPL-2.1");
        LICENSE_ALIASES.put("GNU LESSER GENERAL PUBLIC LICENSE V3", "LGPL-3.0");
        LICENSE_ALIASES.put("GNU LESSER GENERAL PUBLIC LICENSE VERSION 3", "LGPL-3.0");

        // Apache 别名
        LICENSE_ALIASES.put("APACHE LICENSE VERSION 2.0", "Apache-2.0");
        LICENSE_ALIASES.put("APACHE LICENSE 2.0", "Apache-2.0");
        LICENSE_ALIASES.put("THE APACHE SOFTWARE LICENSE VERSION 2.0", "Apache-2.0");

        // MIT 别名
        LICENSE_ALIASES.put("MIT LICENSE", "MIT");
        LICENSE_ALIASES.put("THE MIT LICENSE", "MIT");

        // BSD 别名
        LICENSE_ALIASES.put("BSD 3-CLAUSE LICENSE", "BSD-3-Clause");
        LICENSE_ALIASES.put("BSD 2-CLAUSE LICENSE", "BSD-2-Clause");
        LICENSE_ALIASES.put("THE BSD LICENSE", "BSD-3-Clause");
    }

    /**
     * 扫描依赖的许可证合规性
     *
     * @param dependencies 依赖列表
     * @return 扫描结果
     */
    public LicenseScanResult scan(List<DependencyInfo> dependencies) {
        System.out.println("📜 开始许可证合规性扫描...");
        System.out.println("📊 扫描 " + dependencies.size() + " 个依赖项...");

        LicenseScanResult result = new LicenseScanResult();
        result.setTotalDependencies(dependencies.size());

        for (DependencyInfo dependency : dependencies) {
            System.out.print("  📜 检查: " + dependency.getGroupId() + ":" +
                            dependency.getArtifactId() + ":" + dependency.getVersion());

            List<LicenseInfo> licenseInfos = scanDependencyLicense(dependency);

            if (!licenseInfos.isEmpty()) {
                for (LicenseInfo licenseInfo : licenseInfos) {
                    result.addLicenseInfo(licenseInfo);

                    if (licenseInfo.isRestricted()) {
                        System.out.println("\n    ⚠️  发现限制性许可证: " + licenseInfo.getLicenseName() +
                                         " (" + licenseInfo.getRiskLevel() + ")");
                    }
                }
            }

            if (licenseInfos.isEmpty()) {
                result.incrementUnknownLicenseCount();
                System.out.println("\n    ❓ 未找到许可证信息");
            } else if (licenseInfos.stream().noneMatch(LicenseInfo::isRestricted)) {
                System.out.println("\n    ✅ 许可证合规");
            }
        }

        printScanSummary(result);
        return result;
    }

    /**
     * 扫描单个依赖的许可证
     */
    private List<LicenseInfo> scanDependencyLicense(DependencyInfo dependency) {
        List<LicenseInfo> licenseInfos = new ArrayList<>();

        // 1. 首先查询数据库中的组件许可证信息
        List<ComponentLicenseEntity> componentLicenses = findComponentLicenses(dependency);

        if (!componentLicenses.isEmpty()) {
            // 从数据库中找到许可证信息
            for (ComponentLicenseEntity componentLicense : componentLicenses) {
                LicenseEntity license = componentLicense.getLicense();
                if (license != null) {
                    LicenseInfo licenseInfo = new LicenseInfo(license, dependency);
                    licenseInfo.setDetectionMethod(componentLicense.getDetectionMethod());
                    licenseInfo.setConfidence(componentLicense.getConfidence());
                    licenseInfos.add(licenseInfo);
                }
            }
        } else {
            // 2. 如果数据库中没有，尝试从依赖信息中获取
            List<String> declaredLicenses = dependency.getLicenses();
            if (!declaredLicenses.isEmpty()) {
                for (String declaredLicense : declaredLicenses) {
                    LicenseInfo licenseInfo = createLicenseInfoFromDeclared(declaredLicense, dependency);
                    if (licenseInfo != null) {
                        licenseInfos.add(licenseInfo);
                    }
                }
            }
        }

        return licenseInfos;
    }

    /**
     * 查找组件的许可证信息
     */
    private List<ComponentLicenseEntity> findComponentLicenses(DependencyInfo dependency) {
        // 1. 精确匹配：groupId + artifactId + version
        List<ComponentLicenseEntity> exactMatches = componentLicenseRepository
            .findByGroupIdAndArtifactIdAndVersion(
                dependency.getGroupId(),
                dependency.getArtifactId(),
                dependency.getVersion()
            );

        if (!exactMatches.isEmpty()) {
            return exactMatches;
        }

        // 2. 如果启用模糊匹配，尝试不带版本的匹配
        if (enableFuzzyMatching) {
            List<ComponentLicenseEntity> fuzzyMatches = componentLicenseRepository
                .findByGroupIdAndArtifactId(dependency.getGroupId(), dependency.getArtifactId());

            if (!fuzzyMatches.isEmpty()) {
                return fuzzyMatches;
            }
        }

        return new ArrayList<>();
    }

    /**
     * 从声明的许可证创建LicenseInfo
     */
    private LicenseInfo createLicenseInfoFromDeclared(String declaredLicense, DependencyInfo dependency) {
        if (declaredLicense == null || declaredLicense.trim().isEmpty()) {
            return null;
        }

        // 标准化许可证名称
        String normalizedLicense = normalizeLicenseName(declaredLicense);

        // 尝试从数据库中查找匹配的许可证
        Optional<LicenseEntity> licenseEntity = findLicenseByName(normalizedLicense);

        if (licenseEntity.isPresent()) {
            LicenseInfo licenseInfo = new LicenseInfo(licenseEntity.get(), dependency);
            licenseInfo.setDetectionMethod("POM_DECLARED");
            licenseInfo.setConfidence("MEDIUM");
            licenseInfo.setDeclaredLicense(declaredLicense);
            return licenseInfo;
        } else {
            // 创建未知许可证信息
            LicenseInfo licenseInfo = new LicenseInfo(dependency);
            licenseInfo.setLicenseName(declaredLicense);
            licenseInfo.setNormalizedLicenseName(normalizedLicense);
            licenseInfo.setDetectionMethod("POM_DECLARED");
            licenseInfo.setConfidence("LOW");
            licenseInfo.setDeclaredLicense(declaredLicense);
            licenseInfo.setRestricted(isRestrictedLicense(normalizedLicense));
            licenseInfo.setRiskLevel(determineRiskLevel(normalizedLicense));
            return licenseInfo;
        }
    }

    /**
     * 根据许可证名称查找许可证实体
     */
    private Optional<LicenseEntity> findLicenseByName(String licenseName) {
        // 1. 精确匹配许可证ID
        Optional<LicenseEntity> byId = licenseRepository.findByLicenseId(licenseName);
        if (byId.isPresent()) {
            return byId;
        }

        // 2. 精确匹配SPDX ID
        Optional<LicenseEntity> bySpdx = licenseRepository.findBySpdxId(licenseName);
        if (bySpdx.isPresent()) {
            return bySpdx;
        }

        // 3. 精确匹配名称
        Optional<LicenseEntity> byName = licenseRepository.findByName(licenseName);
        if (byName.isPresent()) {
            return byName;
        }

        // 4. 如果启用模糊匹配，尝试模糊匹配
        if (enableFuzzyMatching) {
            List<LicenseEntity> fuzzyMatches = licenseRepository.findByNameContaining(licenseName);
            if (!fuzzyMatches.isEmpty()) {
                return Optional.of(fuzzyMatches.get(0)); // 返回第一个匹配项
            }
        }

        return Optional.empty();
    }

    /**
     * 判断是否为限制性许可证
     */
    private boolean isRestrictedLicense(String license) {
        if (license == null) {
            return false;
        }

        // 标准化许可证名称
        String normalizedLicense = normalizeLicenseName(license);

        return RESTRICTED_LICENSES.contains(normalizedLicense);
    }

    /**
     * 确定风险等级
     */
    private String determineRiskLevel(String licenseName) {
        if (licenseName == null) {
            return "UNKNOWN";
        }

        String normalized = licenseName.toUpperCase();

        // CRITICAL: 强制开源且网络传播的许可证
        if (normalized.contains("AGPL") || normalized.contains("SSPL")) {
            return "CRITICAL";
        }

        // HIGH: 强制开源的许可证
        if (normalized.contains("GPL") && !normalized.contains("LGPL")) {
            return "HIGH";
        }

        // MEDIUM: 弱Copyleft许可证
        if (normalized.contains("LGPL") || normalized.contains("EPL") ||
            normalized.contains("MPL") || normalized.contains("CDDL")) {
            return "MEDIUM";
        }

        // LOW: 商业友好的许可证
        if (normalized.contains("MIT") || normalized.contains("APACHE") ||
            normalized.contains("BSD") || normalized.contains("ISC")) {
            return "LOW";
        }

        return "UNKNOWN";
    }

    /**
     * 标准化许可证名称
     */
    private String normalizeLicenseName(String license) {
        if (license == null) {
            return "";
        }

        // 移除常见的前缀和后缀
        String normalized = license.trim()
            .replaceAll("(?i)^the\\s+", "")
            .replaceAll("(?i)\\s+license$", "")
            .replaceAll("(?i)\\s+licence$", "")
            .replaceAll("\\s+", " ");

        // 检查别名映射
        String upperNormalized = normalized.toUpperCase();
        for (Map.Entry<String, String> entry : LICENSE_ALIASES.entrySet()) {
            if (upperNormalized.equals(entry.getKey())) {
                return entry.getValue();
            }
        }

        return normalized;
    }

    /**
     * 打印扫描摘要
     */
    private void printScanSummary(LicenseScanResult result) {
        System.out.println("✅ 许可证扫描完成");
        System.out.println("   - 总计: " + result.getTotalDependencies() + " 个依赖");
        System.out.println("   - 限制性: " + result.getRestrictedLicenseCount() + " 个");
        System.out.println("   - 兼容性: " + result.getCompatibleLicenseCount() + " 个");
        System.out.println("   - 未知: " + result.getUnknownLicenseCount() + " 个");

        // 按风险等级统计
        Map<String, Integer> riskStats = result.getRiskLevelStatistics();
        if (!riskStats.isEmpty()) {
            System.out.println("   风险等级分布:");
            riskStats.entrySet().stream()
                .sorted(Map.Entry.<String, Integer>comparingByValue().reversed())
                .forEach(entry -> System.out.println("     " + entry.getKey() + ": " + entry.getValue() + " 个"));
        }
    }
}
