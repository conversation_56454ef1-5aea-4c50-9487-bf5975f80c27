package com.security.checker.scanner;

import org.springframework.stereotype.Component;
import org.springframework.beans.factory.annotation.Value;
import com.security.checker.resolver.MavenDependencyResolver.DependencyInfo;

import java.util.List;
import java.util.Arrays;
import java.util.Set;
import java.util.HashSet;

/**
 * 许可证合规性扫描器
 * 
 * 功能：
 * 1. 检查依赖的许可证类型
 * 2. 识别商业不友好的许可证
 * 3. 许可证兼容性分析
 * 4. 生成合规性报告
 * 
 * <AUTHOR>
 */
@Component
public class LicenseScanner {

    @Value("${security-checker.license.local-license-db:./database/license_data.db}")
    private String localLicenseDb;

    // 商业不友好的许可证列表
    private static final Set<String> RESTRICTED_LICENSES = new HashSet<>(Arrays.asList(
        // 强制开源类（Copyleft）
        "GPL-2.0", "GPL-2.0-only", "GPL-2.0-or-later",
        "GPL-3.0", "GPL-3.0-only", "GPL-3.0-or-later", 
        "AGPL-3.0", "AGPL-3.0-only", "AGPL-3.0-or-later",
        "LGPL-2.1", "LGPL-2.1-only", "LGPL-2.1-or-later",
        "LGPL-3.0", "LGPL-3.0-only", "LGPL-3.0-or-later",
        "EUPL-1.2", "OSL-3.0",
        
        // 商业限制类
        "SSPL-1.0", "BUSL-1.1", "Commons Clause",
        "CPAL-1.0", "CDDL-1.0", "CDDL-1.1",
        
        // 其他风险许可证
        "EPL-1.0", "EPL-2.0", "MPL-2.0"
    ));

    /**
     * 扫描依赖的许可证合规性
     * 
     * @param dependencies 依赖列表
     * @return 扫描结果
     */
    public LicenseScanResult scan(List<DependencyInfo> dependencies) {
        LicenseScanResult result = new LicenseScanResult();
        
        System.out.println("📜 开始许可证合规性扫描...");
        
        for (DependencyInfo dependency : dependencies) {
            scanDependencyLicense(dependency, result);
        }
        
        System.out.println("✅ 许可证扫描完成，发现 " + result.getRestrictedLicenseCount() + " 个限制性许可证");
        
        return result;
    }

    /**
     * 扫描单个依赖的许可证
     */
    private void scanDependencyLicense(DependencyInfo dependency, LicenseScanResult result) {
        // TODO: 实现许可证数据库查询逻辑
        // 1. 从Maven Central获取许可证信息
        // 2. 查询本地许可证数据库
        // 3. 分析许可证兼容性
        // 4. 标记风险等级
        
        System.out.println("  检查许可证: " + dependency.getCoordinate());
        
        // 临时模拟：检查是否包含已知的限制性许可证
        List<String> licenses = dependency.getLicenses();
        for (String license : licenses) {
            if (isRestrictedLicense(license)) {
                result.addRestrictedDependency(dependency, license);
            }
        }
    }

    /**
     * 判断是否为限制性许可证
     */
    private boolean isRestrictedLicense(String license) {
        if (license == null) {
            return false;
        }
        
        // 标准化许可证名称
        String normalizedLicense = normalizeLicenseName(license);
        
        return RESTRICTED_LICENSES.contains(normalizedLicense);
    }

    /**
     * 标准化许可证名称
     */
    private String normalizeLicenseName(String license) {
        if (license == null) {
            return "";
        }
        
        // 移除常见的前缀和后缀
        String normalized = license.trim()
            .replaceAll("(?i)^the\\s+", "")
            .replaceAll("(?i)\\s+license$", "")
            .replaceAll("(?i)\\s+licence$", "");
        
        // 处理常见的别名
        switch (normalized.toUpperCase()) {
            case "GNU GENERAL PUBLIC LICENSE V2":
            case "GNU GENERAL PUBLIC LICENSE VERSION 2":
                return "GPL-2.0";
            case "GNU GENERAL PUBLIC LICENSE V3":
            case "GNU GENERAL PUBLIC LICENSE VERSION 3":
                return "GPL-3.0";
            case "GNU AFFERO GENERAL PUBLIC LICENSE V3":
                return "AGPL-3.0";
            case "GNU LESSER GENERAL PUBLIC LICENSE V2.1":
                return "LGPL-2.1";
            case "GNU LESSER GENERAL PUBLIC LICENSE V3":
                return "LGPL-3.0";
            default:
                return normalized;
        }
    }

    /**
     * 许可证扫描结果类
     */
    public static class LicenseScanResult {
        private int totalDependencies = 0;
        private int restrictedLicenseCount = 0;
        private int unknownLicenseCount = 0;
        private int compatibleLicenseCount = 0;

        public void addRestrictedDependency(DependencyInfo dependency, String license) {
            restrictedLicenseCount++;
            System.out.println("⚠️  发现限制性许可证: " + dependency.getCoordinate() + " -> " + license);
        }

        public int getTotalDependencies() { return totalDependencies; }
        public void setTotalDependencies(int totalDependencies) { this.totalDependencies = totalDependencies; }

        public int getRestrictedLicenseCount() { return restrictedLicenseCount; }
        public void setRestrictedLicenseCount(int restrictedLicenseCount) { this.restrictedLicenseCount = restrictedLicenseCount; }

        public int getUnknownLicenseCount() { return unknownLicenseCount; }
        public void setUnknownLicenseCount(int unknownLicenseCount) { this.unknownLicenseCount = unknownLicenseCount; }

        public int getCompatibleLicenseCount() { return compatibleLicenseCount; }
        public void setCompatibleLicenseCount(int compatibleLicenseCount) { this.compatibleLicenseCount = compatibleLicenseCount; }
    }
}
