package com.security.checker.scanner;

import com.security.checker.entity.CveEntity;
import com.security.checker.entity.CveAffectedComponentEntity;
import com.security.checker.repository.CveRepository;
import com.security.checker.resolver.MavenDependencyResolver.DependencyInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.ArrayList;
import java.util.Set;
import java.util.HashSet;
import java.util.regex.Pattern;

/**
 * CVE漏洞扫描器
 * 
 * 功能：
 * 1. 离线CVE漏洞数据库查询
 * 2. 依赖版本匹配
 * 3. 漏洞严重级别评估
 * 4. CVSS评分计算
 * 
 * <AUTHOR>
 */
@Component
public class CVEScanner {

    @Autowired
    private CveRepository cveRepository;

    @Value("${security-checker.cve.local-db-path:./database/cve_data.db}")
    private String localDbPath;

    @Value("${security-checker.report.severity-threshold:MEDIUM}")
    private String severityThreshold;

    @Value("${security-checker.cve.enable-fuzzy-matching:true}")
    private boolean enableFuzzyMatching;

    /**
     * 扫描依赖的CVE漏洞
     *
     * @param dependencies 依赖列表
     * @return 扫描结果
     */
    public CVEScanResult scan(List<DependencyInfo> dependencies) {
        CVEScanResult result = new CVEScanResult();

        System.out.println("🔍 开始CVE漏洞扫描...");
        System.out.println("📊 扫描 " + dependencies.size() + " 个依赖项...");

        for (DependencyInfo dependency : dependencies) {
            scanDependency(dependency, result);
        }

        System.out.println("✅ CVE扫描完成，发现 " + result.getTotalVulnerabilities() + " 个漏洞");
        System.out.println("   - 严重: " + result.getCriticalCount() + " 个");
        System.out.println("   - 高危: " + result.getHighCount() + " 个");
        System.out.println("   - 中等: " + result.getMediumCount() + " 个");
        System.out.println("   - 低危: " + result.getLowCount() + " 个");

        return result;
    }

    /**
     * 扫描单个依赖
     */
    private void scanDependency(DependencyInfo dependency, CVEScanResult result) {
        System.out.println("  🔍 检查: " + dependency.getCoordinate());

        List<CveEntity> vulnerabilities = findVulnerabilities(dependency);

        for (CveEntity cve : vulnerabilities) {
            if (isVulnerabilityApplicable(cve, dependency)) {
                VulnerabilityInfo vulnInfo = createVulnerabilityInfo(cve, dependency);
                result.addVulnerability(vulnInfo);

                // 更新依赖的CVE列表
                dependency.getCveIds().add(cve.getCveId());

                System.out.println("    ⚠️  发现漏洞: " + cve.getCveId() + " (" + cve.getSeverity() + ")");
            }
        }

        if (vulnerabilities.isEmpty()) {
            System.out.println("    ✅ 未发现已知漏洞");
        }
    }

    /**
     * 查找依赖的漏洞
     */
    private List<CveEntity> findVulnerabilities(DependencyInfo dependency) {
        List<CveEntity> vulnerabilities = new ArrayList<>();

        // 1. 精确匹配：使用artifactId作为产品名称
        String artifactId = dependency.getArtifactId();
        List<CveEntity> exactMatches = cveRepository.findByAffectedProduct(artifactId);
        vulnerabilities.addAll(exactMatches);

        // 2. 模糊匹配：如果启用了模糊匹配
        if (enableFuzzyMatching) {
            // 尝试使用groupId的最后一部分
            String[] groupParts = dependency.getGroupId().split("\\.");
            if (groupParts.length > 0) {
                String lastGroupPart = groupParts[groupParts.length - 1];
                if (!lastGroupPart.equals(artifactId)) {
                    List<CveEntity> fuzzyMatches = cveRepository.findByAffectedProduct(lastGroupPart);
                    vulnerabilities.addAll(fuzzyMatches);
                }
            }

            // 尝试使用完整的groupId
            List<CveEntity> groupMatches = cveRepository.findByAffectedProduct(dependency.getGroupId());
            vulnerabilities.addAll(groupMatches);
        }

        // 去重
        Set<String> seenCveIds = new HashSet<>();
        List<CveEntity> uniqueVulnerabilities = new ArrayList<>();
        for (CveEntity cve : vulnerabilities) {
            if (seenCveIds.add(cve.getCveId())) {
                uniqueVulnerabilities.add(cve);
            }
        }

        return uniqueVulnerabilities;
    }

    /**
     * 检查漏洞是否适用于指定依赖
     */
    private boolean isVulnerabilityApplicable(CveEntity cve, DependencyInfo dependency) {
        String version = dependency.getVersion();
        if (version == null || version.isEmpty()) {
            return false;
        }

        // 检查所有受影响的组件
        for (CveAffectedComponentEntity component : cve.getAffectedComponents()) {
            if (component.getVulnerable() && isVersionInRange(version, component)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 检查版本是否在受影响范围内
     */
    private boolean isVersionInRange(String version, CveAffectedComponentEntity component) {
        try {
            // 使用组件实体的版本匹配方法
            return component.isVersionAffected(version);
        } catch (Exception e) {
            // 如果版本比较失败，采用保守策略：认为可能受影响
            System.out.println("    ⚠️  版本比较失败: " + version + " vs " +
                             component.getVersionStartIncluding() + "-" + component.getVersionEndExcluding());
            return true;
        }
    }

    /**
     * 创建漏洞信息对象
     */
    private VulnerabilityInfo createVulnerabilityInfo(CveEntity cve, DependencyInfo dependency) {
        VulnerabilityInfo vulnInfo = new VulnerabilityInfo();
        vulnInfo.setCveId(cve.getCveId());
        vulnInfo.setDescription(cve.getDescription());
        vulnInfo.setSeverity(cve.getSeverity());
        vulnInfo.setCvssScore(cve.getCvssScore());
        vulnInfo.setPublishedDate(cve.getPublishedDate());
        vulnInfo.setLastModifiedDate(cve.getLastModifiedDate());
        vulnInfo.setAffectedDependency(dependency.getCoordinate());
        vulnInfo.setDependencyVersion(dependency.getVersion());
        vulnInfo.setReferences(cve.getReferences());

        return vulnInfo;
    }

    /**
     * CVE扫描结果类
     */
    public static class CVEScanResult {
        private List<VulnerabilityInfo> vulnerabilities = new ArrayList<>();
        private int totalVulnerabilities = 0;
        private int criticalCount = 0;
        private int highCount = 0;
        private int mediumCount = 0;
        private int lowCount = 0;

        public void addVulnerability(VulnerabilityInfo vulnerability) {
            vulnerabilities.add(vulnerability);
            totalVulnerabilities++;

            // 根据严重程度更新计数
            String severity = vulnerability.getSeverity();
            if ("CRITICAL".equals(severity)) {
                criticalCount++;
            } else if ("HIGH".equals(severity)) {
                highCount++;
            } else if ("MEDIUM".equals(severity)) {
                mediumCount++;
            } else if ("LOW".equals(severity)) {
                lowCount++;
            }
        }

        public List<VulnerabilityInfo> getVulnerabilities() { return vulnerabilities; }
        public void setVulnerabilities(List<VulnerabilityInfo> vulnerabilities) { this.vulnerabilities = vulnerabilities; }

        public int getTotalVulnerabilities() { return totalVulnerabilities; }
        public void setTotalVulnerabilities(int totalVulnerabilities) { this.totalVulnerabilities = totalVulnerabilities; }

        public int getCriticalCount() { return criticalCount; }
        public void setCriticalCount(int criticalCount) { this.criticalCount = criticalCount; }

        public int getHighCount() { return highCount; }
        public void setHighCount(int highCount) { this.highCount = highCount; }

        public int getMediumCount() { return mediumCount; }
        public void setMediumCount(int mediumCount) { this.mediumCount = mediumCount; }

        public int getLowCount() { return lowCount; }
        public void setLowCount(int lowCount) { this.lowCount = lowCount; }

        public boolean hasHighSeverityVulnerabilities() {
            return criticalCount > 0 || highCount > 0;
        }

        public double getAverageCvssScore() {
            if (vulnerabilities.isEmpty()) {
                return 0.0;
            }

            double total = 0.0;
            int count = 0;
            for (VulnerabilityInfo vuln : vulnerabilities) {
                if (vuln.getCvssScore() != null) {
                    total += vuln.getCvssScore();
                    count++;
                }
            }

            return count > 0 ? total / count : 0.0;
        }
    }
}
