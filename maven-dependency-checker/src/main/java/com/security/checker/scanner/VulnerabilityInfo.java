package com.security.checker.scanner;

import java.time.LocalDateTime;

/**
 * 漏洞信息类
 * 封装CVE漏洞的详细信息
 */
public class VulnerabilityInfo {
    
    private String cveId;
    private String description;
    private String severity;
    private Double cvssScore;
    private LocalDateTime publishedDate;
    private LocalDateTime lastModifiedDate;
    private String affectedDependency;
    private String dependencyVersion;
    private String references;
    private String status;
    
    // 构造函数
    public VulnerabilityInfo() {}
    
    public VulnerabilityInfo(String cveId, String description, String severity) {
        this.cveId = cveId;
        this.description = description;
        this.severity = severity;
    }
    
    // Getters and Setters
    public String getCveId() { return cveId; }
    public void setCveId(String cveId) { this.cveId = cveId; }
    
    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }
    
    public String getSeverity() { return severity; }
    public void setSeverity(String severity) { this.severity = severity; }
    
    public Double getCvssScore() { return cvssScore; }
    public void setCvssScore(Double cvssScore) { this.cvssScore = cvssScore; }
    
    public LocalDateTime getPublishedDate() { return publishedDate; }
    public void setPublishedDate(LocalDateTime publishedDate) { this.publishedDate = publishedDate; }
    
    public LocalDateTime getLastModifiedDate() { return lastModifiedDate; }
    public void setLastModifiedDate(LocalDateTime lastModifiedDate) { this.lastModifiedDate = lastModifiedDate; }
    
    public String getAffectedDependency() { return affectedDependency; }
    public void setAffectedDependency(String affectedDependency) { this.affectedDependency = affectedDependency; }
    
    public String getDependencyVersion() { return dependencyVersion; }
    public void setDependencyVersion(String dependencyVersion) { this.dependencyVersion = dependencyVersion; }
    
    public String getReferences() { return references; }
    public void setReferences(String references) { this.references = references; }
    
    public String getStatus() { return status; }
    public void setStatus(String status) { this.status = status; }
    
    // 便利方法
    public boolean isCritical() {
        return "CRITICAL".equals(severity);
    }
    
    public boolean isHigh() {
        return "HIGH".equals(severity);
    }
    
    public boolean isMedium() {
        return "MEDIUM".equals(severity);
    }
    
    public boolean isLow() {
        return "LOW".equals(severity);
    }
    
    public boolean isHighSeverity() {
        return isCritical() || isHigh();
    }
    
    public String getSeverityLevel() {
        if (cvssScore == null) {
            return severity != null ? severity : "UNKNOWN";
        }
        
        if (cvssScore >= 9.0) {
            return "CRITICAL";
        } else if (cvssScore >= 7.0) {
            return "HIGH";
        } else if (cvssScore >= 4.0) {
            return "MEDIUM";
        } else {
            return "LOW";
        }
    }
    
    public String getFormattedCvssScore() {
        return cvssScore != null ? String.format("%.1f", cvssScore) : "N/A";
    }
    
    public String getSummary() {
        return String.format("%s (%s) - %s", 
                           cveId, 
                           severity, 
                           affectedDependency);
    }
    
    @Override
    public String toString() {
        return "VulnerabilityInfo{" +
                "cveId='" + cveId + '\'' +
                ", severity='" + severity + '\'' +
                ", cvssScore=" + cvssScore +
                ", affectedDependency='" + affectedDependency + '\'' +
                ", dependencyVersion='" + dependencyVersion + '\'' +
                '}';
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        
        VulnerabilityInfo that = (VulnerabilityInfo) o;
        
        return cveId != null ? cveId.equals(that.cveId) : that.cveId == null;
    }
    
    @Override
    public int hashCode() {
        return cveId != null ? cveId.hashCode() : 0;
    }
}
