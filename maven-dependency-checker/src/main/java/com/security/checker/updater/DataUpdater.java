package com.security.checker.updater;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.security.checker.entity.CveEntity;
import com.security.checker.entity.DataSyncStatusEntity;
import com.security.checker.entity.LicenseEntity;
import com.security.checker.repository.CveRepository;
import com.security.checker.repository.DataSyncStatusRepository;
import com.security.checker.repository.LicenseRepository;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Optional;

/**
 * 数据更新器
 *
 * 功能：
 * 1. 从NVD更新CVE数据
 * 2. 从SPDX更新许可证数据
 * 3. 支持增量更新
 * 4. 数据验证和清理
 *
 * <AUTHOR>
 */
@Component
public class DataUpdater {

    private static final Logger logger = LoggerFactory.getLogger(DataUpdater.class);
    private final ObjectMapper objectMapper = new ObjectMapper();

    @Value("${security-checker.cve.nvd-api-url}")
    private String nvdApiUrl;

    @Value("${security-checker.cve.nvd-api-key:}")
    private String nvdApiKey;

    @Value("${security-checker.license.spdx-license-url}")
    private String spdxLicenseUrl;

    @Autowired
    private CveRepository cveRepository;

    @Autowired
    private LicenseRepository licenseRepository;

    @Autowired
    private DataSyncStatusRepository dataSyncStatusRepository;

    /**
     * 更新CVE数据
     */
    @Transactional
    public void updateCVEData() throws Exception {
        System.out.println("📥 更新CVE漏洞数据...");
        logger.info("开始更新CVE数据");

        // 获取同步状态
        Optional<DataSyncStatusEntity> syncStatusOpt = dataSyncStatusRepository
            .findByDataTypeAndDataSource("CVE", "NVD");

        DataSyncStatusEntity syncStatus;
        if (syncStatusOpt.isPresent()) {
            syncStatus = syncStatusOpt.get();
        } else {
            syncStatus = new DataSyncStatusEntity("CVE", "NVD");
            syncStatus.setSyncIntervalHours(24);
            syncStatus.setAutoSyncEnabled(true);
            syncStatus.setMaxRetries(3);
        }

        // 检查是否正在同步
        if (syncStatus.isSyncing()) {
            System.out.println("⚠️  CVE数据正在同步中，跳过本次更新");
            return;
        }

        try {
            // 开始同步
            syncStatus.startSync();
            dataSyncStatusRepository.save(syncStatus);

            // 获取增量更新的时间范围
            String lastModifiedParam = "";
            if (syncStatus.getLastSyncTime() != null) {
                // 增量更新：从上次同步时间开始
                lastModifiedParam = "&lastModStartDate=" +
                    syncStatus.getLastSyncTime().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
                System.out.println("🔄 执行增量更新，从 " + syncStatus.getLastSyncTime() + " 开始");
            } else {
                System.out.println("🔄 执行全量更新（注意：为避免API限流，将分批获取数据）");
            }

            // 构建API URL - 使用较小的批次大小避免限流
            String apiUrl = nvdApiUrl + "?resultsPerPage=100" + lastModifiedParam;
            System.out.println("⚠️  注意：NVD API有限流机制，请耐心等待...");

            // 获取CVE数据
            List<CveEntity> newCves = fetchCVEDataFromNVD(apiUrl, syncStatus);

            // 保存到数据库
            if (!newCves.isEmpty()) {
                cveRepository.saveAll(newCves);
                syncStatus.setNewRecords((long) newCves.size());
                System.out.println("💾 保存了 " + newCves.size() + " 个CVE记录");
            } else {
                System.out.println("ℹ️  没有新的CVE数据需要更新");
            }

            // 完成同步
            syncStatus.completeSync(true);
            syncStatus.setTotalRecords((long) newCves.size());
            dataSyncStatusRepository.save(syncStatus);

            System.out.println("✅ CVE数据更新完成");
            logger.info("CVE数据更新完成，新增 {} 条记录", newCves.size());

        } catch (Exception e) {
            logger.error("CVE数据更新失败", e);
            syncStatus.completeSync(false);
            syncStatus.setErrorMessage(e.getMessage());
            dataSyncStatusRepository.save(syncStatus);

            System.out.println("❌ CVE数据更新失败: " + e.getMessage());
            throw e;
        }
    }

    /**
     * 从NVD API获取CVE数据
     */
    private List<CveEntity> fetchCVEDataFromNVD(String apiUrl, DataSyncStatusEntity syncStatus) throws IOException {
        List<CveEntity> cveList = new ArrayList<>();

        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpGet request = new HttpGet(apiUrl);
            request.setHeader("Accept", "application/json");
            request.setHeader("User-Agent", "Maven-Dependency-Checker/1.0");

            // 添加API Key（如果配置了）
            if (nvdApiKey != null && !nvdApiKey.trim().isEmpty()) {
                request.setHeader("apiKey", nvdApiKey);
                System.out.println("🔑 使用NVD API Key进行请求（更高限制）");
            } else {
                System.out.println("⚠️  未配置NVD API Key，使用公共限制（建议申请API Key）");
            }

            System.out.println("🌐 请求NVD API: " + apiUrl);

            // 添加延迟避免限流
            try {
                if (nvdApiKey == null || nvdApiKey.trim().isEmpty()) {
                    // 无API Key：每30秒5个请求，安全起见等待7秒
                    System.out.println("⏳ 等待7秒避免API限流...");
                    Thread.sleep(7000);
                } else {
                    // 有API Key：每30秒50个请求，等待1秒
                    System.out.println("⏳ 等待1秒...");
                    Thread.sleep(1000);
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException("请求被中断", e);
            }

            try (CloseableHttpResponse response = httpClient.execute(request)) {
                int statusCode = response.getStatusLine().getStatusCode();

                if (statusCode == 200) {
                    String jsonResponse = EntityUtils.toString(response.getEntity());
                    JsonNode rootNode = objectMapper.readTree(jsonResponse);

                    // 解析CVE数据
                    JsonNode vulnerabilities = rootNode.path("vulnerabilities");
                    if (vulnerabilities.isArray()) {
                        int totalCount = rootNode.path("totalResults").asInt(0);
                        syncStatus.setTotalRecords((long) totalCount);

                        int processed = 0;
                        for (JsonNode vulnNode : vulnerabilities) {
                            try {
                                CveEntity cve = parseCVEFromJson(vulnNode.path("cve"));
                                if (cve != null) {
                                    cveList.add(cve);
                                }
                                processed++;

                                // 更新进度
                                if (totalCount > 0) {
                                    int progress = (processed * 100) / totalCount;
                                    syncStatus.setProgressPercentage(progress);
                                }

                                if (processed % 100 == 0) {
                                    System.out.println("📊 已处理 " + processed + " 个CVE记录");
                                }
                            } catch (Exception e) {
                                logger.warn("解析CVE记录失败: {}", e.getMessage());
                                syncStatus.setFailedRecords(syncStatus.getFailedRecords() + 1);
                            }
                        }

                        System.out.println("📊 共处理 " + processed + " 个CVE记录");
                    }
                } else {
                    throw new IOException("NVD API请求失败，状态码: " + statusCode);
                }
            }
        }

        return cveList;
    }

    /**
     * 从JSON解析CVE实体
     */
    private CveEntity parseCVEFromJson(JsonNode cveNode) {
        try {
            String cveId = cveNode.path("id").asText();
            if (cveId.isEmpty()) {
                return null;
            }

            // 检查是否已存在
            Optional<CveEntity> existingCve = cveRepository.findByCveId(cveId);
            CveEntity cve = existingCve.orElse(new CveEntity());

            cve.setCveId(cveId);

            // 解析描述
            JsonNode descriptions = cveNode.path("descriptions");
            if (descriptions.isArray()) {
                for (JsonNode desc : descriptions) {
                    if ("en".equals(desc.path("lang").asText())) {
                        cve.setDescription(desc.path("value").asText());
                        break;
                    }
                }
            }

            // 解析CVSS评分
            JsonNode metrics = cveNode.path("metrics");
            parseCVSSMetrics(metrics, cve);

            // 解析发布和修改时间
            String publishedDate = cveNode.path("published").asText();
            String lastModifiedDate = cveNode.path("lastModified").asText();

            if (!publishedDate.isEmpty()) {
                cve.setPublishedDate(LocalDateTime.parse(publishedDate.replace("Z", "")));
            }
            if (!lastModifiedDate.isEmpty()) {
                cve.setLastModifiedDate(LocalDateTime.parse(lastModifiedDate.replace("Z", "")));
            }

            // 解析受影响的产品
            JsonNode configurations = cveNode.path("configurations");
            parseAffectedProducts(configurations, cve);

            return cve;

        } catch (Exception e) {
            logger.warn("解析CVE JSON失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 解析CVSS评分
     */
    private void parseCVSSMetrics(JsonNode metrics, CveEntity cve) {
        // 优先使用CVSS v3.1，然后v3.0，最后v2.0
        JsonNode cvssV31 = metrics.path("cvssMetricV31");
        JsonNode cvssV30 = metrics.path("cvssMetricV30");
        JsonNode cvssV2 = metrics.path("cvssMetricV2");

        if (cvssV31.isArray() && cvssV31.size() > 0) {
            JsonNode cvss = cvssV31.get(0).path("cvssData");
            cve.setCvssScore(cvss.path("baseScore").asDouble(0.0));
            cve.setSeverity(cvss.path("baseSeverity").asText("UNKNOWN"));
            cve.setCvssVector(cvss.path("vectorString").asText());
        } else if (cvssV30.isArray() && cvssV30.size() > 0) {
            JsonNode cvss = cvssV30.get(0).path("cvssData");
            cve.setCvssScore(cvss.path("baseScore").asDouble(0.0));
            cve.setSeverity(cvss.path("baseSeverity").asText("UNKNOWN"));
            cve.setCvssVector(cvss.path("vectorString").asText());
        } else if (cvssV2.isArray() && cvssV2.size() > 0) {
            JsonNode cvss = cvssV2.get(0).path("cvssData");
            cve.setCvssScore(cvss.path("baseScore").asDouble(0.0));
            // CVSS v2没有severity字段，根据分数计算
            double score = cvss.path("baseScore").asDouble(0.0);
            if (score >= 7.0) {
                cve.setSeverity("HIGH");
            } else if (score >= 4.0) {
                cve.setSeverity("MEDIUM");
            } else {
                cve.setSeverity("LOW");
            }
            cve.setCvssVector(cvss.path("vectorString").asText());
        }
    }

    /**
     * 解析受影响的产品
     */
    private void parseAffectedProducts(JsonNode configurations, CveEntity cve) {
        StringBuilder affectedProducts = new StringBuilder();

        if (configurations.isArray()) {
            for (JsonNode config : configurations) {
                JsonNode nodes = config.path("nodes");
                if (nodes.isArray()) {
                    for (JsonNode node : nodes) {
                        JsonNode cpeMatches = node.path("cpeMatch");
                        if (cpeMatches.isArray()) {
                            for (JsonNode cpeMatch : cpeMatches) {
                                String cpe23Uri = cpeMatch.path("criteria").asText();
                                if (!cpe23Uri.isEmpty()) {
                                    if (affectedProducts.length() > 0) {
                                        affectedProducts.append(";");
                                    }
                                    affectedProducts.append(cpe23Uri);
                                }
                            }
                        }
                    }
                }
            }
        }

        if (affectedProducts.length() > 0) {
            // 限制长度避免数据库字段溢出
            String products = affectedProducts.toString();
            if (products.length() > 4000) {
                products = products.substring(0, 4000) + "...";
            }
            cve.setConfigurations(products);
        }
    }

    /**
     * 更新许可证数据
     */
    @Transactional
    public void updateLicenseData() throws Exception {
        System.out.println("📥 更新许可证数据...");
        logger.info("开始更新许可证数据");

        // 获取同步状态
        Optional<DataSyncStatusEntity> syncStatusOpt = dataSyncStatusRepository
            .findByDataTypeAndDataSource("LICENSE", "SPDX");

        DataSyncStatusEntity syncStatus;
        if (syncStatusOpt.isPresent()) {
            syncStatus = syncStatusOpt.get();
        } else {
            syncStatus = new DataSyncStatusEntity("LICENSE", "SPDX");
            syncStatus.setSyncIntervalHours(168); // 一周更新一次
            syncStatus.setAutoSyncEnabled(true);
            syncStatus.setMaxRetries(3);
        }

        // 检查是否正在同步
        if (syncStatus.isSyncing()) {
            System.out.println("⚠️  许可证数据正在同步中，跳过本次更新");
            return;
        }

        try {
            // 开始同步
            syncStatus.startSync();
            dataSyncStatusRepository.save(syncStatus);

            // 获取SPDX许可证数据
            List<LicenseEntity> newLicenses = fetchLicenseDataFromSPDX(syncStatus);

            // 保存到数据库
            if (!newLicenses.isEmpty()) {
                licenseRepository.saveAll(newLicenses);
                syncStatus.setNewRecords((long) newLicenses.size());
                System.out.println("💾 保存了 " + newLicenses.size() + " 个许可证记录");
            } else {
                System.out.println("ℹ️  没有新的许可证数据需要更新");
            }

            // 完成同步
            syncStatus.completeSync(true);
            syncStatus.setTotalRecords((long) newLicenses.size());
            dataSyncStatusRepository.save(syncStatus);

            System.out.println("✅ 许可证数据更新完成");
            logger.info("许可证数据更新完成，新增 {} 条记录", newLicenses.size());

        } catch (Exception e) {
            logger.error("许可证数据更新失败", e);
            syncStatus.completeSync(false);
            syncStatus.setErrorMessage(e.getMessage());
            dataSyncStatusRepository.save(syncStatus);

            System.out.println("❌ 许可证数据更新失败: " + e.getMessage());
            throw e;
        }
    }

    /**
     * 从SPDX获取许可证数据
     */
    private List<LicenseEntity> fetchLicenseDataFromSPDX(DataSyncStatusEntity syncStatus) throws IOException {
        List<LicenseEntity> licenseList = new ArrayList<>();

        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpGet request = new HttpGet(spdxLicenseUrl);
            request.setHeader("Accept", "application/json");
            request.setHeader("User-Agent", "Maven-Dependency-Checker/1.0");

            System.out.println("🌐 请求SPDX API: " + spdxLicenseUrl);

            try (CloseableHttpResponse response = httpClient.execute(request)) {
                int statusCode = response.getStatusLine().getStatusCode();

                if (statusCode == 200) {
                    String jsonResponse = EntityUtils.toString(response.getEntity());
                    JsonNode rootNode = objectMapper.readTree(jsonResponse);

                    // 解析许可证数据
                    JsonNode licenses = rootNode.path("licenses");
                    if (licenses.isArray()) {
                        int totalCount = licenses.size();
                        syncStatus.setTotalRecords((long) totalCount);

                        int processed = 0;
                        for (JsonNode licenseNode : licenses) {
                            try {
                                LicenseEntity license = parseLicenseFromJson(licenseNode);
                                if (license != null) {
                                    licenseList.add(license);
                                }
                                processed++;

                                // 更新进度
                                int progress = (processed * 100) / totalCount;
                                syncStatus.setProgressPercentage(progress);

                                if (processed % 50 == 0) {
                                    System.out.println("📊 已处理 " + processed + " 个许可证记录");
                                }
                            } catch (Exception e) {
                                logger.warn("解析许可证记录失败: {}", e.getMessage());
                                syncStatus.setFailedRecords(syncStatus.getFailedRecords() + 1);
                            }
                        }

                        System.out.println("📊 共处理 " + processed + " 个许可证记录");
                    }
                } else {
                    throw new IOException("SPDX API请求失败，状态码: " + statusCode);
                }
            }
        }

        return licenseList;
    }

    /**
     * 从JSON解析许可证实体
     */
    private LicenseEntity parseLicenseFromJson(JsonNode licenseNode) {
        try {
            String licenseId = licenseNode.path("licenseId").asText();
            if (licenseId.isEmpty()) {
                return null;
            }

            // 检查是否已存在
            Optional<LicenseEntity> existingLicense = licenseRepository.findBySpdxId(licenseId);
            LicenseEntity license = existingLicense.orElse(new LicenseEntity());

            license.setSpdxId(licenseId);
            license.setName(licenseNode.path("name").asText());
            license.setUrl(licenseNode.path("reference").asText());

            // 判断是否为OSI批准的许可证
            boolean osiApproved = licenseNode.path("isOsiApproved").asBoolean(false);
            // 注意：LicenseEntity没有osiApproved字段，我们可以通过其他方式处理

            // 判断是否已弃用
            boolean deprecated = licenseNode.path("isDeprecatedLicenseId").asBoolean(false);
            license.setDeprecated(deprecated);

            // 根据许可证ID判断商业友好性和风险等级
            assessLicenseRisk(license);

            return license;

        } catch (Exception e) {
            logger.warn("解析许可证JSON失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 评估许可证风险
     */
    private void assessLicenseRisk(LicenseEntity license) {
        String spdxId = license.getSpdxId().toUpperCase();

        // 设置默认值
        license.setCommercialFriendly(true);
        license.setRiskLevel("LOW");
        license.setCopyleftType("NONE");
        // 注意：LicenseEntity没有restricted字段，我们通过commercialFriendly来判断

        // 高风险许可证（强Copyleft）
        if (spdxId.startsWith("AGPL") || spdxId.equals("SSPL-1.0") || spdxId.equals("BUSL-1.1")) {
            license.setCommercialFriendly(false);
            license.setRiskLevel("CRITICAL");
            license.setCopyleftType("NETWORK");
            license.setRequiresSourceDisclosure(true);
        }
        // GPL系列（强Copyleft）
        else if (spdxId.startsWith("GPL-")) {
            license.setCommercialFriendly(false);
            license.setRiskLevel("HIGH");
            license.setCopyleftType("STRONG");
            license.setRequiresSourceDisclosure(true);
        }
        // 弱Copyleft许可证
        else if (spdxId.startsWith("LGPL-") || spdxId.startsWith("EPL-") ||
                 spdxId.startsWith("MPL-") || spdxId.startsWith("EUPL-") ||
                 spdxId.startsWith("CDDL-") || spdxId.equals("OSL-3.0")) {
            license.setCommercialFriendly(false);
            license.setRiskLevel("MEDIUM");
            license.setCopyleftType("WEAK");
            license.setRequiresSourceDisclosure(true);
        }
        // 商业友好许可证
        else if (spdxId.startsWith("MIT") || spdxId.startsWith("APACHE-") ||
                 spdxId.startsWith("BSD-") || spdxId.equals("ISC") ||
                 spdxId.equals("UNLICENSE") || spdxId.equals("CC0-1.0")) {
            license.setCommercialFriendly(true);
            license.setRiskLevel("LOW");
            license.setCopyleftType("NONE");
            license.setRequiresSourceDisclosure(false);
        }
    }
}
