package com.security.checker.entity;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 数据同步状态实体类
 * 记录CVE和许可证数据的同步状态和历史
 */
@Entity
@Table(name = "data_sync_status", indexes = {
    @Index(name = "idx_data_type", columnList = "data_type"),
    @Index(name = "idx_sync_status", columnList = "sync_status"),
    @Index(name = "idx_last_sync", columnList = "last_sync_time")
})
public class DataSyncStatusEntity {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * 数据类型：CVE, LICENSE, COMPONENT_METADATA
     */
    @Column(name = "data_type", nullable = false, length = 30)
    private String dataType;
    
    /**
     * 数据源：NVD, SPDX, MAVEN_CENTRAL, GITHUB
     */
    @Column(name = "data_source", nullable = false, length = 50)
    private String dataSource;
    
    /**
     * 同步状态：NEVER_SYNCED, IN_PROGRESS, SUCCESS, FAILED, PARTIAL
     */
    @Column(name = "sync_status", nullable = false, length = 20)
    private String syncStatus = "NEVER_SYNCED";
    
    /**
     * 最后同步时间
     */
    @Column(name = "last_sync_time")
    private LocalDateTime lastSyncTime;
    
    /**
     * 下次计划同步时间
     */
    @Column(name = "next_sync_time")
    private LocalDateTime nextSyncTime;
    
    /**
     * 同步开始时间
     */
    @Column(name = "sync_start_time")
    private LocalDateTime syncStartTime;
    
    /**
     * 同步结束时间
     */
    @Column(name = "sync_end_time")
    private LocalDateTime syncEndTime;
    
    /**
     * 同步的记录总数
     */
    @Column(name = "total_records")
    private Long totalRecords = 0L;
    
    /**
     * 新增记录数
     */
    @Column(name = "new_records")
    private Long newRecords = 0L;
    
    /**
     * 更新记录数
     */
    @Column(name = "updated_records")
    private Long updatedRecords = 0L;
    
    /**
     * 删除记录数
     */
    @Column(name = "deleted_records")
    private Long deletedRecords = 0L;
    
    /**
     * 失败记录数
     */
    @Column(name = "failed_records")
    private Long failedRecords = 0L;
    
    /**
     * 同步进度百分比
     */
    @Column(name = "progress_percentage")
    private Integer progressPercentage = 0;
    
    /**
     * 错误消息
     */
    @Column(name = "error_message", columnDefinition = "TEXT")
    private String errorMessage;
    
    /**
     * 同步配置（JSON格式）
     */
    @Column(name = "sync_config", columnDefinition = "TEXT")
    private String syncConfig;
    
    /**
     * 数据版本或ETag
     */
    @Column(name = "data_version", length = 100)
    private String dataVersion;
    
    /**
     * 最后修改时间（数据源的最后修改时间）
     */
    @Column(name = "last_modified")
    private LocalDateTime lastModified;
    
    /**
     * 同步间隔（小时）
     */
    @Column(name = "sync_interval_hours")
    private Integer syncIntervalHours = 24;
    
    /**
     * 是否启用自动同步
     */
    @Column(name = "auto_sync_enabled")
    private Boolean autoSyncEnabled = true;
    
    /**
     * 重试次数
     */
    @Column(name = "retry_count")
    private Integer retryCount = 0;
    
    /**
     * 最大重试次数
     */
    @Column(name = "max_retries")
    private Integer maxRetries = 3;
    
    /**
     * 创建时间
     */
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;
    
    // 构造函数
    public DataSyncStatusEntity() {
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }
    
    public DataSyncStatusEntity(String dataType, String dataSource) {
        this();
        this.dataType = dataType;
        this.dataSource = dataSource;
    }
    
    // JPA生命周期回调
    @PreUpdate
    public void preUpdate() {
        this.updatedAt = LocalDateTime.now();
    }
    
    // Getters and Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
    
    public String getDataType() { return dataType; }
    public void setDataType(String dataType) { this.dataType = dataType; }
    
    public String getDataSource() { return dataSource; }
    public void setDataSource(String dataSource) { this.dataSource = dataSource; }
    
    public String getSyncStatus() { return syncStatus; }
    public void setSyncStatus(String syncStatus) { this.syncStatus = syncStatus; }
    
    public LocalDateTime getLastSyncTime() { return lastSyncTime; }
    public void setLastSyncTime(LocalDateTime lastSyncTime) { this.lastSyncTime = lastSyncTime; }
    
    public LocalDateTime getNextSyncTime() { return nextSyncTime; }
    public void setNextSyncTime(LocalDateTime nextSyncTime) { this.nextSyncTime = nextSyncTime; }
    
    public LocalDateTime getSyncStartTime() { return syncStartTime; }
    public void setSyncStartTime(LocalDateTime syncStartTime) { this.syncStartTime = syncStartTime; }
    
    public LocalDateTime getSyncEndTime() { return syncEndTime; }
    public void setSyncEndTime(LocalDateTime syncEndTime) { this.syncEndTime = syncEndTime; }
    
    public Long getTotalRecords() { return totalRecords; }
    public void setTotalRecords(Long totalRecords) { this.totalRecords = totalRecords; }
    
    public Long getNewRecords() { return newRecords; }
    public void setNewRecords(Long newRecords) { this.newRecords = newRecords; }
    
    public Long getUpdatedRecords() { return updatedRecords; }
    public void setUpdatedRecords(Long updatedRecords) { this.updatedRecords = updatedRecords; }
    
    public Long getDeletedRecords() { return deletedRecords; }
    public void setDeletedRecords(Long deletedRecords) { this.deletedRecords = deletedRecords; }
    
    public Long getFailedRecords() { return failedRecords; }
    public void setFailedRecords(Long failedRecords) { this.failedRecords = failedRecords; }
    
    public Integer getProgressPercentage() { return progressPercentage; }
    public void setProgressPercentage(Integer progressPercentage) { this.progressPercentage = progressPercentage; }
    
    public String getErrorMessage() { return errorMessage; }
    public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
    
    public String getSyncConfig() { return syncConfig; }
    public void setSyncConfig(String syncConfig) { this.syncConfig = syncConfig; }
    
    public String getDataVersion() { return dataVersion; }
    public void setDataVersion(String dataVersion) { this.dataVersion = dataVersion; }
    
    public LocalDateTime getLastModified() { return lastModified; }
    public void setLastModified(LocalDateTime lastModified) { this.lastModified = lastModified; }
    
    public Integer getSyncIntervalHours() { return syncIntervalHours; }
    public void setSyncIntervalHours(Integer syncIntervalHours) { this.syncIntervalHours = syncIntervalHours; }
    
    public Boolean getAutoSyncEnabled() { return autoSyncEnabled; }
    public void setAutoSyncEnabled(Boolean autoSyncEnabled) { this.autoSyncEnabled = autoSyncEnabled; }
    
    public Integer getRetryCount() { return retryCount; }
    public void setRetryCount(Integer retryCount) { this.retryCount = retryCount; }
    
    public Integer getMaxRetries() { return maxRetries; }
    public void setMaxRetries(Integer maxRetries) { this.maxRetries = maxRetries; }
    
    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }
    
    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }
    
    // 便利方法
    
    /**
     * 判断是否需要同步
     */
    public boolean needsSync() {
        if (!autoSyncEnabled) {
            return false;
        }
        
        if (lastSyncTime == null) {
            return true;
        }
        
        LocalDateTime nextSync = lastSyncTime.plusHours(syncIntervalHours);
        return LocalDateTime.now().isAfter(nextSync);
    }
    
    /**
     * 判断是否正在同步
     */
    public boolean isSyncing() {
        return "IN_PROGRESS".equals(syncStatus);
    }
    
    /**
     * 判断上次同步是否成功
     */
    public boolean isLastSyncSuccessful() {
        return "SUCCESS".equals(syncStatus);
    }
    
    /**
     * 判断是否可以重试
     */
    public boolean canRetry() {
        return retryCount < maxRetries;
    }
    
    /**
     * 开始同步
     */
    public void startSync() {
        this.syncStatus = "IN_PROGRESS";
        this.syncStartTime = LocalDateTime.now();
        this.syncEndTime = null;
        this.progressPercentage = 0;
        this.errorMessage = null;
    }
    
    /**
     * 完成同步
     */
    public void completeSync(boolean success) {
        this.syncStatus = success ? "SUCCESS" : "FAILED";
        this.syncEndTime = LocalDateTime.now();
        this.lastSyncTime = this.syncEndTime;
        this.progressPercentage = 100;
        
        if (success) {
            this.retryCount = 0;
            this.nextSyncTime = this.lastSyncTime.plusHours(syncIntervalHours);
        } else {
            this.retryCount++;
        }
    }
    
    /**
     * 获取同步持续时间（秒）
     */
    public Long getSyncDurationSeconds() {
        if (syncStartTime == null) {
            return null;
        }
        
        LocalDateTime endTime = syncEndTime != null ? syncEndTime : LocalDateTime.now();
        return java.time.Duration.between(syncStartTime, endTime).getSeconds();
    }
    
    @Override
    public String toString() {
        return "DataSyncStatusEntity{" +
                "id=" + id +
                ", dataType='" + dataType + '\'' +
                ", dataSource='" + dataSource + '\'' +
                ", syncStatus='" + syncStatus + '\'' +
                ", lastSyncTime=" + lastSyncTime +
                ", totalRecords=" + totalRecords +
                '}';
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof DataSyncStatusEntity)) return false;
        DataSyncStatusEntity that = (DataSyncStatusEntity) o;
        return id != null && id.equals(that.id);
    }
    
    @Override
    public int hashCode() {
        return id != null ? id.hashCode() : 0;
    }
}
