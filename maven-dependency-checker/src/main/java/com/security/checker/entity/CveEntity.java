package com.security.checker.entity;

import javax.persistence.*;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * CVE漏洞实体类
 * 存储从NVD获取的CVE漏洞信息
 */
@Entity
@Table(name = "cve_vulnerabilities", indexes = {
    @Index(name = "idx_cve_id", columnList = "cve_id"),
    @Index(name = "idx_severity", columnList = "severity"),
    @Index(name = "idx_published_date", columnList = "published_date"),
    @Index(name = "idx_last_modified", columnList = "last_modified_date")
})
public class CveEntity {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * CVE编号，如 CVE-2023-12345
     */
    @Column(name = "cve_id", unique = true, nullable = false, length = 20)
    private String cveId;
    
    /**
     * 漏洞描述
     */
    @Column(name = "description", columnDefinition = "TEXT")
    private String description;
    
    /**
     * CVSS v3.1 基础分数
     */
    @Column(name = "cvss_score")
    private Double cvssScore;
    
    /**
     * CVSS v3.1 向量字符串
     */
    @Column(name = "cvss_vector", length = 100)
    private String cvssVector;
    
    /**
     * 严重程度: CRITICAL, HIGH, MEDIUM, LOW
     */
    @Column(name = "severity", length = 20)
    private String severity;
    
    /**
     * 发布日期
     */
    @Column(name = "published_date")
    private LocalDateTime publishedDate;
    
    /**
     * 最后修改日期
     */
    @Column(name = "last_modified_date")
    private LocalDateTime lastModifiedDate;
    
    /**
     * 漏洞状态: ANALYZED, AWAITING_ANALYSIS, UNDERGOING_ANALYSIS, REJECTED
     */
    @Column(name = "status", length = 30)
    private String status;
    
    /**
     * 受影响的产品配置（JSON格式存储）
     */
    @Column(name = "configurations", columnDefinition = "TEXT")
    private String configurations;
    
    /**
     * 参考链接（JSON格式存储）
     */
    @Column(name = "references", columnDefinition = "TEXT")
    private String references;
    
    /**
     * 数据源
     */
    @Column(name = "source", length = 50, nullable = false)
    private String source = "NVD";
    
    /**
     * 创建时间
     */
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;
    
    /**
     * 关联的受影响组件
     */
    @OneToMany(mappedBy = "cve", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<CveAffectedComponentEntity> affectedComponents = new ArrayList<>();
    
    // 构造函数
    public CveEntity() {
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }
    
    public CveEntity(String cveId) {
        this();
        this.cveId = cveId;
    }
    
    // JPA生命周期回调
    @PreUpdate
    public void preUpdate() {
        this.updatedAt = LocalDateTime.now();
    }
    
    // Getters and Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
    
    public String getCveId() { return cveId; }
    public void setCveId(String cveId) { this.cveId = cveId; }
    
    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }
    
    public Double getCvssScore() { return cvssScore; }
    public void setCvssScore(Double cvssScore) { this.cvssScore = cvssScore; }
    
    public String getCvssVector() { return cvssVector; }
    public void setCvssVector(String cvssVector) { this.cvssVector = cvssVector; }
    
    public String getSeverity() { return severity; }
    public void setSeverity(String severity) { this.severity = severity; }
    
    public LocalDateTime getPublishedDate() { return publishedDate; }
    public void setPublishedDate(LocalDateTime publishedDate) { this.publishedDate = publishedDate; }
    
    public LocalDateTime getLastModifiedDate() { return lastModifiedDate; }
    public void setLastModifiedDate(LocalDateTime lastModifiedDate) { this.lastModifiedDate = lastModifiedDate; }
    
    public String getStatus() { return status; }
    public void setStatus(String status) { this.status = status; }
    
    public String getConfigurations() { return configurations; }
    public void setConfigurations(String configurations) { this.configurations = configurations; }
    
    public String getReferences() { return references; }
    public void setReferences(String references) { this.references = references; }
    
    public String getSource() { return source; }
    public void setSource(String source) { this.source = source; }
    
    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }
    
    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }
    
    public List<CveAffectedComponentEntity> getAffectedComponents() { return affectedComponents; }
    public void setAffectedComponents(List<CveAffectedComponentEntity> affectedComponents) { this.affectedComponents = affectedComponents; }
    
    // 便利方法
    public void addAffectedComponent(CveAffectedComponentEntity component) {
        affectedComponents.add(component);
        component.setCve(this);
    }
    
    public void removeAffectedComponent(CveAffectedComponentEntity component) {
        affectedComponents.remove(component);
        component.setCve(null);
    }
    
    /**
     * 获取严重程度的数值表示（用于排序）
     */
    public int getSeverityLevel() {
        if (severity == null) return 0;
        switch (severity.toUpperCase()) {
            case "CRITICAL": return 4;
            case "HIGH": return 3;
            case "MEDIUM": return 2;
            case "LOW": return 1;
            default: return 0;
        }
    }
    
    @Override
    public String toString() {
        return "CveEntity{" +
                "id=" + id +
                ", cveId='" + cveId + '\'' +
                ", severity='" + severity + '\'' +
                ", cvssScore=" + cvssScore +
                ", publishedDate=" + publishedDate +
                '}';
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof CveEntity)) return false;
        CveEntity cveEntity = (CveEntity) o;
        return cveId != null && cveId.equals(cveEntity.cveId);
    }
    
    @Override
    public int hashCode() {
        return cveId != null ? cveId.hashCode() : 0;
    }
}
