package com.security.checker.entity;

import javax.persistence.*;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 许可证实体类
 * 存储软件许可证信息和商业友好性评估
 */
@Entity
@Table(name = "licenses", indexes = {
    @Index(name = "idx_license_id", columnList = "license_id"),
    @Index(name = "idx_spdx_id", columnList = "spdx_id"),
    @Index(name = "idx_commercial_friendly", columnList = "commercial_friendly"),
    @Index(name = "idx_copyleft_type", columnList = "copyleft_type")
})
public class LicenseEntity {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * 许可证标识符（SPDX ID或自定义ID）
     */
    @Column(name = "license_id", unique = true, nullable = false, length = 100)
    private String licenseId;
    
    /**
     * SPDX许可证标识符
     */
    @Column(name = "spdx_id", length = 100)
    private String spdxId;
    
    /**
     * 许可证名称
     */
    @Column(name = "name", nullable = false, length = 200)
    private String name;
    
    /**
     * 许可证全文
     */
    @Column(name = "text", columnDefinition = "TEXT")
    private String text;
    
    /**
     * 许可证URL
     */
    @Column(name = "url", length = 500)
    private String url;
    
    /**
     * 是否商业友好
     */
    @Column(name = "commercial_friendly", nullable = false)
    private Boolean commercialFriendly = true;
    
    /**
     * Copyleft类型：NONE, WEAK, STRONG, NETWORK
     */
    @Column(name = "copyleft_type", length = 20)
    private String copyleftType = "NONE";
    
    /**
     * 风险等级：LOW, MEDIUM, HIGH, CRITICAL
     */
    @Column(name = "risk_level", length = 20)
    private String riskLevel = "LOW";
    
    /**
     * 是否需要源码公开
     */
    @Column(name = "requires_source_disclosure")
    private Boolean requiresSourceDisclosure = false;
    
    /**
     * 是否允许商业使用
     */
    @Column(name = "allows_commercial_use")
    private Boolean allowsCommercialUse = true;
    
    /**
     * 是否允许修改
     */
    @Column(name = "allows_modification")
    private Boolean allowsModification = true;
    
    /**
     * 是否允许分发
     */
    @Column(name = "allows_distribution")
    private Boolean allowsDistribution = true;
    
    /**
     * 是否允许私人使用
     */
    @Column(name = "allows_private_use")
    private Boolean allowsPrivateUse = true;
    
    /**
     * 是否需要包含许可证
     */
    @Column(name = "requires_license_inclusion")
    private Boolean requiresLicenseInclusion = true;
    
    /**
     * 是否需要包含版权声明
     */
    @Column(name = "requires_copyright_notice")
    private Boolean requiresCopyrightNotice = true;
    
    /**
     * 是否需要声明修改
     */
    @Column(name = "requires_state_changes")
    private Boolean requiresStateChanges = false;
    
    /**
     * 是否提供专利授权
     */
    @Column(name = "provides_patent_grant")
    private Boolean providesPatentGrant = false;
    
    /**
     * 许可证类别
     */
    @Column(name = "category", length = 50)
    private String category;
    
    /**
     * 许可证描述
     */
    @Column(name = "description", columnDefinition = "TEXT")
    private String description;
    
    /**
     * 是否已弃用
     */
    @Column(name = "deprecated")
    private Boolean deprecated = false;
    
    /**
     * 数据源
     */
    @Column(name = "source", length = 50, nullable = false)
    private String source = "SPDX";
    
    /**
     * 创建时间
     */
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;
    
    /**
     * 使用此许可证的组件
     */
    @OneToMany(mappedBy = "license", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<ComponentLicenseEntity> components = new ArrayList<>();
    
    // 构造函数
    public LicenseEntity() {
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }
    
    public LicenseEntity(String licenseId, String name) {
        this();
        this.licenseId = licenseId;
        this.name = name;
    }
    
    // JPA生命周期回调
    @PreUpdate
    public void preUpdate() {
        this.updatedAt = LocalDateTime.now();
    }
    
    // Getters and Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
    
    public String getLicenseId() { return licenseId; }
    public void setLicenseId(String licenseId) { this.licenseId = licenseId; }
    
    public String getSpdxId() { return spdxId; }
    public void setSpdxId(String spdxId) { this.spdxId = spdxId; }
    
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }
    
    public String getText() { return text; }
    public void setText(String text) { this.text = text; }
    
    public String getUrl() { return url; }
    public void setUrl(String url) { this.url = url; }
    
    public Boolean getCommercialFriendly() { return commercialFriendly; }
    public void setCommercialFriendly(Boolean commercialFriendly) { this.commercialFriendly = commercialFriendly; }
    
    public String getCopyleftType() { return copyleftType; }
    public void setCopyleftType(String copyleftType) { this.copyleftType = copyleftType; }
    
    public String getRiskLevel() { return riskLevel; }
    public void setRiskLevel(String riskLevel) { this.riskLevel = riskLevel; }
    
    public Boolean getRequiresSourceDisclosure() { return requiresSourceDisclosure; }
    public void setRequiresSourceDisclosure(Boolean requiresSourceDisclosure) { this.requiresSourceDisclosure = requiresSourceDisclosure; }
    
    public Boolean getAllowsCommercialUse() { return allowsCommercialUse; }
    public void setAllowsCommercialUse(Boolean allowsCommercialUse) { this.allowsCommercialUse = allowsCommercialUse; }
    
    public Boolean getAllowsModification() { return allowsModification; }
    public void setAllowsModification(Boolean allowsModification) { this.allowsModification = allowsModification; }
    
    public Boolean getAllowsDistribution() { return allowsDistribution; }
    public void setAllowsDistribution(Boolean allowsDistribution) { this.allowsDistribution = allowsDistribution; }
    
    public Boolean getAllowsPrivateUse() { return allowsPrivateUse; }
    public void setAllowsPrivateUse(Boolean allowsPrivateUse) { this.allowsPrivateUse = allowsPrivateUse; }
    
    public Boolean getRequiresLicenseInclusion() { return requiresLicenseInclusion; }
    public void setRequiresLicenseInclusion(Boolean requiresLicenseInclusion) { this.requiresLicenseInclusion = requiresLicenseInclusion; }
    
    public Boolean getRequiresCopyrightNotice() { return requiresCopyrightNotice; }
    public void setRequiresCopyrightNotice(Boolean requiresCopyrightNotice) { this.requiresCopyrightNotice = requiresCopyrightNotice; }
    
    public Boolean getRequiresStateChanges() { return requiresStateChanges; }
    public void setRequiresStateChanges(Boolean requiresStateChanges) { this.requiresStateChanges = requiresStateChanges; }
    
    public Boolean getProvidesPatentGrant() { return providesPatentGrant; }
    public void setProvidesPatentGrant(Boolean providesPatentGrant) { this.providesPatentGrant = providesPatentGrant; }
    
    public String getCategory() { return category; }
    public void setCategory(String category) { this.category = category; }
    
    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }
    
    public Boolean getDeprecated() { return deprecated; }
    public void setDeprecated(Boolean deprecated) { this.deprecated = deprecated; }
    
    public String getSource() { return source; }
    public void setSource(String source) { this.source = source; }
    
    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }
    
    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }
    
    public List<ComponentLicenseEntity> getComponents() { return components; }
    public void setComponents(List<ComponentLicenseEntity> components) { this.components = components; }
    
    // 便利方法
    
    /**
     * 获取风险等级的数值表示（用于排序）
     */
    public int getRiskLevelValue() {
        if (riskLevel == null) return 0;
        switch (riskLevel.toUpperCase()) {
            case "CRITICAL": return 4;
            case "HIGH": return 3;
            case "MEDIUM": return 2;
            case "LOW": return 1;
            default: return 0;
        }
    }
    
    /**
     * 判断是否为Copyleft许可证
     */
    public boolean isCopyleft() {
        return copyleftType != null && !"NONE".equals(copyleftType.toUpperCase());
    }
    
    /**
     * 判断是否为强Copyleft许可证
     */
    public boolean isStrongCopyleft() {
        return "STRONG".equals(copyleftType) || "NETWORK".equals(copyleftType);
    }
    
    @Override
    public String toString() {
        return "LicenseEntity{" +
                "id=" + id +
                ", licenseId='" + licenseId + '\'' +
                ", name='" + name + '\'' +
                ", commercialFriendly=" + commercialFriendly +
                ", copyleftType='" + copyleftType + '\'' +
                ", riskLevel='" + riskLevel + '\'' +
                '}';
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof LicenseEntity)) return false;
        LicenseEntity that = (LicenseEntity) o;
        return licenseId != null && licenseId.equals(that.licenseId);
    }
    
    @Override
    public int hashCode() {
        return licenseId != null ? licenseId.hashCode() : 0;
    }
}
