package com.security.checker.entity;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * CVE受影响组件实体类
 * 存储CVE漏洞影响的具体组件信息
 */
@Entity
@Table(name = "cve_affected_components", indexes = {
    @Index(name = "idx_vendor_product", columnList = "vendor, product"),
    @Index(name = "idx_product_version", columnList = "product, version_start_including, version_end_excluding"),
    @Index(name = "idx_cve_id", columnList = "cve_id")
})
public class CveAffectedComponentEntity {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * 关联的CVE漏洞
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cve_id", nullable = false)
    private CveEntity cve;
    
    /**
     * 供应商名称
     */
    @Column(name = "vendor", length = 100)
    private String vendor;
    
    /**
     * 产品名称
     */
    @Column(name = "product", length = 100, nullable = false)
    private String product;
    
    /**
     * 版本起始（包含）
     */
    @Column(name = "version_start_including", length = 50)
    private String versionStartIncluding;
    
    /**
     * 版本结束（不包含）
     */
    @Column(name = "version_end_excluding", length = 50)
    private String versionEndExcluding;
    
    /**
     * 版本起始（不包含）
     */
    @Column(name = "version_start_excluding", length = 50)
    private String versionStartExcluding;
    
    /**
     * 版本结束（包含）
     */
    @Column(name = "version_end_including", length = 50)
    private String versionEndIncluding;
    
    /**
     * 是否易受攻击
     */
    @Column(name = "vulnerable", nullable = false)
    private Boolean vulnerable = true;
    
    /**
     * CPE 2.3 URI（通用平台枚举）
     */
    @Column(name = "cpe23_uri", length = 500)
    private String cpe23Uri;
    
    /**
     * 配置节点类型：AND, OR
     */
    @Column(name = "operator", length = 10)
    private String operator;
    
    /**
     * 创建时间
     */
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;
    
    // 构造函数
    public CveAffectedComponentEntity() {
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }
    
    public CveAffectedComponentEntity(String product) {
        this();
        this.product = product;
    }
    
    // JPA生命周期回调
    @PreUpdate
    public void preUpdate() {
        this.updatedAt = LocalDateTime.now();
    }
    
    // Getters and Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
    
    public CveEntity getCve() { return cve; }
    public void setCve(CveEntity cve) { this.cve = cve; }
    
    public String getVendor() { return vendor; }
    public void setVendor(String vendor) { this.vendor = vendor; }
    
    public String getProduct() { return product; }
    public void setProduct(String product) { this.product = product; }
    
    public String getVersionStartIncluding() { return versionStartIncluding; }
    public void setVersionStartIncluding(String versionStartIncluding) { this.versionStartIncluding = versionStartIncluding; }
    
    public String getVersionEndExcluding() { return versionEndExcluding; }
    public void setVersionEndExcluding(String versionEndExcluding) { this.versionEndExcluding = versionEndExcluding; }
    
    public String getVersionStartExcluding() { return versionStartExcluding; }
    public void setVersionStartExcluding(String versionStartExcluding) { this.versionStartExcluding = versionStartExcluding; }
    
    public String getVersionEndIncluding() { return versionEndIncluding; }
    public void setVersionEndIncluding(String versionEndIncluding) { this.versionEndIncluding = versionEndIncluding; }
    
    public Boolean getVulnerable() { return vulnerable; }
    public void setVulnerable(Boolean vulnerable) { this.vulnerable = vulnerable; }
    
    public String getCpe23Uri() { return cpe23Uri; }
    public void setCpe23Uri(String cpe23Uri) { this.cpe23Uri = cpe23Uri; }
    
    public String getOperator() { return operator; }
    public void setOperator(String operator) { this.operator = operator; }
    
    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }
    
    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }
    
    /**
     * 检查给定版本是否在受影响范围内
     * 
     * @param version 要检查的版本
     * @return 如果版本在受影响范围内返回true
     */
    public boolean isVersionAffected(String version) {
        if (version == null || !vulnerable) {
            return false;
        }
        
        // 简单的版本比较逻辑（实际应用中需要更复杂的版本比较算法）
        try {
            // 检查起始版本（包含）
            if (versionStartIncluding != null) {
                if (compareVersions(version, versionStartIncluding) < 0) {
                    return false;
                }
            }
            
            // 检查起始版本（不包含）
            if (versionStartExcluding != null) {
                if (compareVersions(version, versionStartExcluding) <= 0) {
                    return false;
                }
            }
            
            // 检查结束版本（不包含）
            if (versionEndExcluding != null) {
                if (compareVersions(version, versionEndExcluding) >= 0) {
                    return false;
                }
            }
            
            // 检查结束版本（包含）
            if (versionEndIncluding != null) {
                if (compareVersions(version, versionEndIncluding) > 0) {
                    return false;
                }
            }
            
            return true;
            
        } catch (Exception e) {
            // 版本比较失败时，采用保守策略
            return false;
        }
    }
    
    /**
     * 简单的版本比较方法
     * 
     * @param version1 版本1
     * @param version2 版本2
     * @return 负数表示version1 < version2，0表示相等，正数表示version1 > version2
     */
    private int compareVersions(String version1, String version2) {
        if (version1.equals(version2)) {
            return 0;
        }
        
        String[] parts1 = version1.split("\\.");
        String[] parts2 = version2.split("\\.");
        
        int maxLength = Math.max(parts1.length, parts2.length);
        
        for (int i = 0; i < maxLength; i++) {
            int v1 = i < parts1.length ? parseVersionPart(parts1[i]) : 0;
            int v2 = i < parts2.length ? parseVersionPart(parts2[i]) : 0;
            
            if (v1 != v2) {
                return Integer.compare(v1, v2);
            }
        }
        
        return 0;
    }
    
    /**
     * 解析版本部分为数字
     */
    private int parseVersionPart(String part) {
        try {
            // 移除非数字字符（如 "1.0.0-SNAPSHOT" 中的 "-SNAPSHOT"）
            String numericPart = part.replaceAll("[^0-9].*", "");
            return numericPart.isEmpty() ? 0 : Integer.parseInt(numericPart);
        } catch (NumberFormatException e) {
            return 0;
        }
    }
    
    @Override
    public String toString() {
        return "CveAffectedComponentEntity{" +
                "id=" + id +
                ", vendor='" + vendor + '\'' +
                ", product='" + product + '\'' +
                ", versionStartIncluding='" + versionStartIncluding + '\'' +
                ", versionEndExcluding='" + versionEndExcluding + '\'' +
                ", vulnerable=" + vulnerable +
                '}';
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof CveAffectedComponentEntity)) return false;
        CveAffectedComponentEntity that = (CveAffectedComponentEntity) o;
        return id != null && id.equals(that.id);
    }
    
    @Override
    public int hashCode() {
        return id != null ? id.hashCode() : 0;
    }
}
