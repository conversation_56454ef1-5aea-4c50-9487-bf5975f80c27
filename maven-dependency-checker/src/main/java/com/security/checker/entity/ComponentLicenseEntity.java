package com.security.checker.entity;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 组件许可证关联实体类
 * 存储Maven组件与其许可证的关联关系
 */
@Entity
@Table(name = "component_licenses", indexes = {
    @Index(name = "idx_component_coords", columnList = "group_id, artifact_id, version"),
    @Index(name = "idx_license_id", columnList = "license_id"),
    @Index(name = "idx_detection_method", columnList = "detection_method")
})
public class ComponentLicenseEntity {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * Maven组件的groupId
     */
    @Column(name = "group_id", nullable = false, length = 200)
    private String groupId;
    
    /**
     * Maven组件的artifactId
     */
    @Column(name = "artifact_id", nullable = false, length = 200)
    private String artifactId;
    
    /**
     * Maven组件的版本
     */
    @Column(name = "version", nullable = false, length = 100)
    private String version;
    
    /**
     * 组件类型（jar, war, pom等）
     */
    @Column(name = "type", length = 20)
    private String type = "jar";
    
    /**
     * 关联的许可证
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "license_id", nullable = false)
    private LicenseEntity license;
    
    /**
     * 许可证检测方法：POM_DECLARED, JAR_MANIFEST, LICENSE_FILE, HEURISTIC
     */
    @Column(name = "detection_method", length = 30)
    private String detectionMethod;
    
    /**
     * 许可证在POM中的原始声明
     */
    @Column(name = "declared_license", length = 200)
    private String declaredLicense;
    
    /**
     * 许可证文件路径（如果从JAR中检测到）
     */
    @Column(name = "license_file_path", length = 500)
    private String licenseFilePath;
    
    /**
     * 检测置信度：HIGH, MEDIUM, LOW
     */
    @Column(name = "confidence", length = 10)
    private String confidence = "MEDIUM";
    
    /**
     * 是否为主要许可证（一个组件可能有多个许可证）
     */
    @Column(name = "primary_license")
    private Boolean primaryLicense = true;
    
    /**
     * 许可证URL（如果在POM中声明）
     */
    @Column(name = "license_url", length = 500)
    private String licenseUrl;
    
    /**
     * 许可证注释或备注
     */
    @Column(name = "comments", length = 1000)
    private String comments;
    
    /**
     * 数据源
     */
    @Column(name = "source", length = 50, nullable = false)
    private String source = "POM_ANALYSIS";
    
    /**
     * 创建时间
     */
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;
    
    // 构造函数
    public ComponentLicenseEntity() {
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }
    
    public ComponentLicenseEntity(String groupId, String artifactId, String version) {
        this();
        this.groupId = groupId;
        this.artifactId = artifactId;
        this.version = version;
    }
    
    // JPA生命周期回调
    @PreUpdate
    public void preUpdate() {
        this.updatedAt = LocalDateTime.now();
    }
    
    // Getters and Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
    
    public String getGroupId() { return groupId; }
    public void setGroupId(String groupId) { this.groupId = groupId; }
    
    public String getArtifactId() { return artifactId; }
    public void setArtifactId(String artifactId) { this.artifactId = artifactId; }
    
    public String getVersion() { return version; }
    public void setVersion(String version) { this.version = version; }
    
    public String getType() { return type; }
    public void setType(String type) { this.type = type; }
    
    public LicenseEntity getLicense() { return license; }
    public void setLicense(LicenseEntity license) { this.license = license; }
    
    public String getDetectionMethod() { return detectionMethod; }
    public void setDetectionMethod(String detectionMethod) { this.detectionMethod = detectionMethod; }
    
    public String getDeclaredLicense() { return declaredLicense; }
    public void setDeclaredLicense(String declaredLicense) { this.declaredLicense = declaredLicense; }
    
    public String getLicenseFilePath() { return licenseFilePath; }
    public void setLicenseFilePath(String licenseFilePath) { this.licenseFilePath = licenseFilePath; }
    
    public String getConfidence() { return confidence; }
    public void setConfidence(String confidence) { this.confidence = confidence; }
    
    public Boolean getPrimaryLicense() { return primaryLicense; }
    public void setPrimaryLicense(Boolean primaryLicense) { this.primaryLicense = primaryLicense; }
    
    public String getLicenseUrl() { return licenseUrl; }
    public void setLicenseUrl(String licenseUrl) { this.licenseUrl = licenseUrl; }
    
    public String getComments() { return comments; }
    public void setComments(String comments) { this.comments = comments; }
    
    public String getSource() { return source; }
    public void setSource(String source) { this.source = source; }
    
    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }
    
    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }
    
    // 便利方法
    
    /**
     * 获取组件坐标
     */
    public String getCoordinate() {
        return groupId + ":" + artifactId + ":" + version;
    }
    
    /**
     * 获取组件GAV（不包含版本）
     */
    public String getGav() {
        return groupId + ":" + artifactId;
    }
    
    /**
     * 获取置信度的数值表示（用于排序）
     */
    public int getConfidenceLevel() {
        if (confidence == null) return 1;
        switch (confidence.toUpperCase()) {
            case "HIGH": return 3;
            case "MEDIUM": return 2;
            case "LOW": return 1;
            default: return 1;
        }
    }
    
    /**
     * 判断检测方法是否可靠
     */
    public boolean isReliableDetection() {
        return "POM_DECLARED".equals(detectionMethod) || 
               "JAR_MANIFEST".equals(detectionMethod) ||
               "LICENSE_FILE".equals(detectionMethod);
    }
    
    /**
     * 判断是否为高置信度检测
     */
    public boolean isHighConfidence() {
        return "HIGH".equals(confidence);
    }
    
    @Override
    public String toString() {
        return "ComponentLicenseEntity{" +
                "id=" + id +
                ", groupId='" + groupId + '\'' +
                ", artifactId='" + artifactId + '\'' +
                ", version='" + version + '\'' +
                ", license=" + (license != null ? license.getLicenseId() : "null") +
                ", detectionMethod='" + detectionMethod + '\'' +
                ", confidence='" + confidence + '\'' +
                '}';
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof ComponentLicenseEntity)) return false;
        ComponentLicenseEntity that = (ComponentLicenseEntity) o;
        return id != null && id.equals(that.id);
    }
    
    @Override
    public int hashCode() {
        return id != null ? id.hashCode() : 0;
    }
}
