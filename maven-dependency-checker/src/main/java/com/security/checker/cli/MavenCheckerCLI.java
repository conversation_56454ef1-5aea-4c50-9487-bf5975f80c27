package com.security.checker.cli;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import picocli.CommandLine;
import picocli.CommandLine.Command;
import picocli.CommandLine.Option;
import picocli.CommandLine.Parameters;

import com.security.checker.resolver.MavenDependencyResolver;
import com.security.checker.scanner.CVEScanner;
import com.security.checker.scanner.LicenseScanner;
import com.security.checker.report.HTMLReportGenerator;
import com.security.checker.updater.DataUpdater;

import java.io.File;
import java.util.List;
import java.util.concurrent.Callable;

/**
 * Maven依赖安全检查工具命令行接口
 * 
 * 支持的功能：
 * 1. 扫描pom.xml文件
 * 2. 检查CVE漏洞
 * 3. 检查许可证合规性
 * 4. 生成HTML报告
 * 5. 更新漏洞数据库
 * 
 * <AUTHOR>
 */
@Component
@Command(
    name = "maven-checker",
    description = "Maven依赖安全检查工具 - 离线CVE漏洞和许可证合规性检查",
    version = "1.0.0",
    mixinStandardHelpOptions = true
)
public class MavenCheckerCLI implements Callable<Integer> {

    @Autowired
    private MavenDependencyResolver dependencyResolver;
    
    @Autowired
    private CVEScanner cveScanner;
    
    @Autowired
    private LicenseScanner licenseScanner;
    
    @Autowired
    private HTMLReportGenerator reportGenerator;
    
    @Autowired
    private DataUpdater dataUpdater;

    @Parameters(
        index = "0", 
        description = "pom.xml文件路径或包含pom.xml的目录路径",
        arity = "0..1"
    )
    private String pomPath = "./pom.xml";

    @Option(
        names = {"-o", "--output"}, 
        description = "HTML报告输出路径 (默认: ./reports/security-report.html)"
    )
    private String outputPath = "./reports/security-report.html";

    @Option(
        names = {"-u", "--update"}, 
        description = "更新CVE和许可证数据库"
    )
    private boolean updateData = false;

    @Option(
        names = {"-s", "--severity"}, 
        description = "最低严重级别过滤 (LOW, MEDIUM, HIGH, CRITICAL)",
        defaultValue = "MEDIUM"
    )
    private String severityThreshold;

    @Option(
        names = {"-t", "--transitive"}, 
        description = "包含传递依赖检查 (默认: true)"
    )
    private boolean includeTransitive = true;

    @Option(
        names = {"-l", "--license-only"}, 
        description = "仅执行许可证检查"
    )
    private boolean licenseOnly = false;

    @Option(
        names = {"-c", "--cve-only"}, 
        description = "仅执行CVE漏洞检查"
    )
    private boolean cveOnly = false;

    @Option(
        names = {"-v", "--verbose"}, 
        description = "详细输出模式"
    )
    private boolean verbose = false;

    public void execute(String[] args) {
        CommandLine commandLine = new CommandLine(this);
        int exitCode = commandLine.execute(args);
        if (exitCode != 0) {
            System.exit(exitCode);
        }
    }

    @Override
    public Integer call() throws Exception {
        try {
            System.out.println("🔍 Maven依赖安全检查工具启动...");
            
            // 1. 数据更新
            if (updateData) {
                System.out.println("📥 更新安全数据库...");
                dataUpdater.updateCVEData();
                dataUpdater.updateLicenseData();
                System.out.println("✅ 数据库更新完成");
                
                if (pomPath.equals("./pom.xml") && !new File(pomPath).exists()) {
                    System.out.println("ℹ️  仅执行数据更新，未指定pom.xml文件");
                    return 0;
                }
            }

            // 2. 验证pom.xml文件
            File pomFile = validatePomFile(pomPath);
            System.out.println("📋 分析项目: " + pomFile.getAbsolutePath());

            // 3. 解析Maven依赖
            System.out.println("🔄 解析Maven依赖...");
            List<MavenDependencyResolver.DependencyInfo> dependencies = dependencyResolver.resolveDependencies(pomFile, includeTransitive);
            System.out.println("📦 发现 " + dependencies.size() + " 个依赖项");

            // 3.1 检查依赖冲突
            List<MavenDependencyResolver.DependencyConflict> conflicts =
                dependencyResolver.checkDependencyConflicts(dependencies);
            if (!conflicts.isEmpty()) {
                System.out.println("⚠️  发现 " + conflicts.size() + " 个依赖冲突:");
                for (MavenDependencyResolver.DependencyConflict conflict : conflicts) {
                    System.out.println("   " + conflict.toString());
                }
            }

            // 3.2 如果是详细模式，构建并显示依赖树
            if (verbose) {
                try {
                    System.out.println("🌳 构建依赖树...");
                    MavenDependencyResolver.DependencyTreeNode tree =
                        dependencyResolver.buildDependencyTree(pomFile);
                    System.out.println("📋 依赖树结构:");
                    tree.printTree();
                } catch (Exception e) {
                    System.out.println("⚠️  依赖树构建失败，跳过: " + e.getMessage());
                }
            }

            // 4. 执行安全检查
            SecurityScanResult scanResults = performSecurityScan(dependencies);

            // 5. 生成HTML报告
            System.out.println("📊 生成安全报告...");
            File outputFile = new File(outputPath);
            outputFile.getParentFile().mkdirs();
            
            reportGenerator.generateReport(scanResults, outputFile);
            System.out.println("✅ 报告已生成: " + outputFile.getAbsolutePath());

            // 6. 输出摘要
            printSummary(scanResults);

            return 0;
            
        } catch (Exception e) {
            System.err.println("❌ 执行失败: " + e.getMessage());
            if (verbose) {
                e.printStackTrace();
            }
            return 1;
        }
    }

    private File validatePomFile(String path) throws Exception {
        File file = new File(path);
        
        if (!file.exists()) {
            throw new Exception("文件不存在: " + path);
        }
        
        if (file.isDirectory()) {
            file = new File(file, "pom.xml");
            if (!file.exists()) {
                throw new Exception("目录中未找到pom.xml文件: " + path);
            }
        }
        
        if (!file.getName().equals("pom.xml")) {
            throw new Exception("不是有效的pom.xml文件: " + path);
        }
        
        return file;
    }

    private SecurityScanResult performSecurityScan(List<MavenDependencyResolver.DependencyInfo> dependencies) {
        System.out.println("🛡️  执行安全扫描...");

        SecurityScanResult result = new SecurityScanResult();

        if (!licenseOnly) {
            System.out.println("🚨 检查CVE漏洞...");
            CVEScanner.CVEScanResult cveResult = cveScanner.scan(dependencies);
            result.setCveResult(cveResult);
        }

        if (!cveOnly) {
            System.out.println("📜 检查许可证合规性...");
            // TODO: 实现许可证扫描
            // LicenseScanner.LicenseScanResult licenseResult = licenseScanner.scan(dependencies);
            // result.setLicenseResult(licenseResult);
        }

        return result;
    }

    private void printSummary(SecurityScanResult scanResults) {
        System.out.println("\n📋 扫描摘要:");
        System.out.println("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");

        if (scanResults.getCveResult() != null) {
            CVEScanner.CVEScanResult cveResult = scanResults.getCveResult();
            System.out.println("🚨 CVE漏洞检查:");
            System.out.println("   总计: " + cveResult.getTotalVulnerabilities() + " 个漏洞");
            System.out.println("   严重: " + cveResult.getCriticalCount() + " 个");
            System.out.println("   高危: " + cveResult.getHighCount() + " 个");
            System.out.println("   中等: " + cveResult.getMediumCount() + " 个");
            System.out.println("   低危: " + cveResult.getLowCount() + " 个");

            if (cveResult.hasHighSeverityVulnerabilities()) {
                System.out.println("   ⚠️  发现高危漏洞，建议立即处理！");
            }
        }

        if (scanResults.getLicenseResult() != null) {
            System.out.println("📜 许可证检查:");
            System.out.println("   TODO: 实现许可证摘要");
        }

        System.out.println("✅ 扫描完成");
    }
}
