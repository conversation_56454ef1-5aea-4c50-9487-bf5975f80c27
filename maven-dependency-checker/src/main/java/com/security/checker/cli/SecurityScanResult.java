package com.security.checker.cli;

import com.security.checker.scanner.CVEScanner;

/**
 * 安全扫描结果聚合类
 * 包含CVE漏洞扫描和许可证扫描的结果
 */
public class SecurityScanResult {
    
    private CVEScanner.CVEScanResult cveResult;
    private Object licenseResult; // TODO: 替换为LicenseScanner.LicenseScanResult
    
    public SecurityScanResult() {}
    
    public SecurityScanResult(CVEScanner.CVEScanResult cveResult, Object licenseResult) {
        this.cveResult = cveResult;
        this.licenseResult = licenseResult;
    }
    
    // Getters and Setters
    public CVEScanner.CVEScanResult getCveResult() {
        return cveResult;
    }
    
    public void setCveResult(CVEScanner.CVEScanResult cveResult) {
        this.cveResult = cveResult;
    }
    
    public Object getLicenseResult() {
        return licenseResult;
    }
    
    public void setLicenseResult(Object licenseResult) {
        this.licenseResult = licenseResult;
    }
    
    // 便利方法
    public boolean hasVulnerabilities() {
        return cveResult != null && cveResult.getTotalVulnerabilities() > 0;
    }
    
    public boolean hasHighSeverityVulnerabilities() {
        return cveResult != null && cveResult.hasHighSeverityVulnerabilities();
    }
    
    public boolean hasLicenseIssues() {
        // TODO: 实现许可证问题检查
        return false;
    }
    
    public boolean hasAnyIssues() {
        return hasVulnerabilities() || hasLicenseIssues();
    }
    
    public int getTotalIssueCount() {
        int count = 0;
        if (cveResult != null) {
            count += cveResult.getTotalVulnerabilities();
        }
        // TODO: 添加许可证问题计数
        return count;
    }
    
    public String getSummary() {
        StringBuilder summary = new StringBuilder();
        
        if (cveResult != null) {
            summary.append("CVE漏洞: ").append(cveResult.getTotalVulnerabilities()).append("个");
            if (cveResult.hasHighSeverityVulnerabilities()) {
                summary.append(" (包含高危漏洞)");
            }
        }
        
        if (licenseResult != null) {
            if (summary.length() > 0) {
                summary.append(", ");
            }
            summary.append("许可证问题: 待实现");
        }
        
        return summary.toString();
    }
    
    @Override
    public String toString() {
        return "SecurityScanResult{" +
                "cveResult=" + cveResult +
                ", licenseResult=" + licenseResult +
                '}';
    }
}
