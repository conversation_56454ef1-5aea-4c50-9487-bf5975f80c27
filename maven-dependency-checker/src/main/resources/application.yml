# Maven Dependency Security Checker Configuration
# JDK 1.8 Compatible Configuration

spring:
  application:
    name: maven-dependency-checker
  
  # 数据库配置 - 使用H2作为本地数据库
  datasource:
    url: jdbc:h2:file:./database/security_checker;DB_CLOSE_ON_EXIT=FALSE
    driver-class-name: org.h2.Driver
    username: sa
    password: 
    
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    database-platform: org.hibernate.dialect.H2Dialect
    
  # H2控制台配置（开发时使用）
  h2:
    console:
      enabled: true
      path: /h2-console
      
  # Thymeleaf配置
  thymeleaf:
    cache: false
    prefix: classpath:/templates/
    suffix: .html
    encoding: UTF-8

# 应用自定义配置
security-checker:
  # CVE数据源配置
  cve:
    nvd-api-url: https://services.nvd.nist.gov/rest/json/cves/2.0
    local-db-path: ./database/cve_data.db
    update-interval-hours: 24
    enable-fuzzy-matching: true
    
  # 许可证数据配置
  license:
    # 商业不友好的许可证列表
    restricted-licenses:
      - GPL-2.0
      - GPL-3.0
      - AGPL-3.0
      - LGPL-2.1
      - LGPL-3.0
      - EUPL-1.2
      - OSL-3.0
      - SSPL-1.0
      - BUSL-1.1
      - Commons Clause
      - CPAL-1.0
      - CDDL-1.0
      - CDDL-1.1
      - EPL-1.0
      - EPL-2.0
      - MPL-2.0
    
    # 许可证数据源
    spdx-license-url: https://raw.githubusercontent.com/spdx/license-list-data/master/json/licenses.json
    local-license-db: ./database/license_data.db
    
  # 报告配置
  report:
    output-dir: ./reports
    template-dir: classpath:/templates
    include-transitive-deps: true
    severity-threshold: MEDIUM
    
  # Maven配置
  maven:
    local-repository: ~/.m2/repository
    central-url: https://repo1.maven.org/maven2
    timeout-seconds: 30

# 日志配置
logging:
  level:
    com.security.checker: INFO
    org.springframework: WARN
    org.hibernate: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: ./logs/maven-checker.log
