<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON>依赖安全检查报告</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header .subtitle {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .summary-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .summary-card h3 {
            color: #666;
            font-size: 0.9em;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 10px;
        }
        
        .summary-card .number {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .critical { color: #dc3545; }
        .high { color: #fd7e14; }
        .medium { color: #ffc107; }
        .low { color: #28a745; }
        .info { color: #17a2b8; }
        
        .section {
            background: white;
            margin-bottom: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .section-header {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 1px solid #dee2e6;
        }
        
        .section-header h2 {
            color: #495057;
            font-size: 1.5em;
        }
        
        .section-content {
            padding: 20px;
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        
        .table th,
        .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }
        
        .table th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }
        
        .table tr:hover {
            background-color: #f8f9fa;
        }
        
        .badge {
            display: inline-block;
            padding: 4px 8px;
            font-size: 0.75em;
            font-weight: bold;
            border-radius: 4px;
            text-transform: uppercase;
        }
        
        .badge-critical { background-color: #dc3545; color: white; }
        .badge-high { background-color: #fd7e14; color: white; }
        .badge-medium { background-color: #ffc107; color: black; }
        .badge-low { background-color: #28a745; color: white; }
        
        .dependency-tree {
            font-family: 'Courier New', monospace;
            background: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            overflow-x: auto;
        }
        
        .dependency-item {
            margin: 5px 0;
            padding: 5px;
            border-radius: 3px;
        }
        
        .dependency-item.has-issues {
            background-color: #fff3cd;
            border-left: 4px solid #ffc107;
        }
        
        .dependency-item.has-vulnerabilities {
            background-color: #f8d7da;
            border-left: 4px solid #dc3545;
        }
        
        .footer {
            text-align: center;
            padding: 20px;
            color: #666;
            font-size: 0.9em;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .summary-grid {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .table {
                font-size: 0.9em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 报告头部 -->
        <div class="header">
            <h1>🛡️ Maven依赖安全检查报告</h1>
            <div class="subtitle">
                项目: <span th:text="${report.projectName}">Unknown Project</span> |
                生成时间: <span th:text="${generatedTime}">2024-01-01 00:00:00</span>
            </div>
        </div>

        <!-- 摘要统计 -->
        <div class="summary-grid">
            <div class="summary-card">
                <h3>总依赖数</h3>
                <div class="number info" th:text="${summary.totalDependencies}">0</div>
            </div>
            <div class="summary-card">
                <h3>漏洞总数</h3>
                <div class="number critical" th:text="${summary.totalVulnerabilities}">0</div>
            </div>
            <div class="summary-card">
                <h3>严重漏洞</h3>
                <div class="number critical" th:text="${summary.criticalVulnerabilities}">0</div>
            </div>
            <div class="summary-card">
                <h3>高危漏洞</h3>
                <div class="number high" th:text="${summary.highVulnerabilities}">0</div>
            </div>
            <div class="summary-card">
                <h3>许可证问题</h3>
                <div class="number medium" th:text="${summary.totalLicenseIssues}">0</div>
            </div>
        </div>

        <!-- 漏洞详情 -->
        <div class="section" th:if="${not #lists.isEmpty(vulnerabilityStats)}">
            <div class="section-header">
                <h2>🚨 漏洞详情</h2>
            </div>
            <div class="section-content">
                <table class="table">
                    <thead>
                        <tr>
                            <th>依赖</th>
                            <th>版本</th>
                            <th>严重</th>
                            <th>高危</th>
                            <th>中危</th>
                            <th>低危</th>
                            <th>总计</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr th:each="stat : ${vulnerabilityStats}">
                            <td th:text="${stat.dependency}">org.example:library</td>
                            <td th:text="${stat.version}">1.0.0</td>
                            <td><span class="badge badge-critical" th:text="${stat.critical}" th:if="${stat.critical > 0}">0</span></td>
                            <td><span class="badge badge-high" th:text="${stat.high}" th:if="${stat.high > 0}">0</span></td>
                            <td><span class="badge badge-medium" th:text="${stat.medium}" th:if="${stat.medium > 0}">0</span></td>
                            <td><span class="badge badge-low" th:text="${stat.low}" th:if="${stat.low > 0}">0</span></td>
                            <td><strong th:text="${stat.total}">0</strong></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 许可证统计 -->
        <div class="section" th:if="${not #lists.isEmpty(licenseStats)}">
            <div class="section-header">
                <h2>📄 许可证统计</h2>
            </div>
            <div class="section-content">
                <table class="table">
                    <thead>
                        <tr>
                            <th>许可证</th>
                            <th>风险等级</th>
                            <th>商业友好</th>
                            <th>使用次数</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr th:each="stat : ${licenseStats}">
                            <td th:text="${stat.licenseName}">Apache-2.0</td>
                            <td>
                                <span class="badge" 
                                      th:classappend="${stat.riskLevel == 'HIGH'} ? 'badge-critical' : (${stat.riskLevel == 'MEDIUM'} ? 'badge-medium' : 'badge-low')"
                                      th:text="${stat.riskLevel}">LOW</span>
                            </td>
                            <td th:text="${stat.commercialFriendly} ? '✅ 是' : '❌ 否'">✅ 是</td>
                            <td th:text="${stat.count}">1</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 依赖树 -->
        <div class="section" th:if="${not #lists.isEmpty(dependencyTree)}">
            <div class="section-header">
                <h2>🌳 依赖关系</h2>
            </div>
            <div class="section-content">
                <div class="dependency-tree">
                    <div th:each="node : ${dependencyTree}" 
                         class="dependency-item"
                         th:classappend="${node.hasVulnerabilities} ? 'has-vulnerabilities' : (${node.hasLicenseIssues} ? 'has-issues' : '')">
                        <span th:text="${node.dependency.groupId + ':' + node.dependency.artifactId + ':' + node.dependency.version}">
                            org.example:library:1.0.0
                        </span>
                        <span th:if="${node.hasVulnerabilities}" style="color: #dc3545; margin-left: 10px;">⚠️ 有漏洞</span>
                        <span th:if="${node.hasLicenseIssues}" style="color: #ffc107; margin-left: 10px;">📄 许可证风险</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 页脚 -->
        <div class="footer">
            <p>报告由 Maven依赖安全检查工具 生成 | 数据来源: NVD, SPDX</p>
        </div>
    </div>
</body>
</html>
