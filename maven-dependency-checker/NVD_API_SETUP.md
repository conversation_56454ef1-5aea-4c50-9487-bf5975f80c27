# NVD API Key 配置指南

## 为什么需要API Key？

NVD (National Vulnerability Database) API对请求有限制：
- **无API Key**: 每30秒最多5个请求
- **有API Key**: 每30秒最多50个请求

配置API Key可以显著提高数据同步速度，强烈建议申请。

## 如何获取NVD API Key

1. 访问 [NVD API Key申请页面](https://nvd.nist.gov/developers/request-an-api-key)
2. 填写申请表单：
   - 提供有效的邮箱地址
   - 说明使用目的（例如：Maven依赖安全检查）
   - 同意使用条款
3. 提交申请后，通常几分钟内会收到API Key邮件

## 配置API Key

### 方法1：环境变量（推荐）

```bash
# Linux/macOS
export NVD_API_KEY="your-api-key-here"

# Windows
set NVD_API_KEY=your-api-key-here
```

### 方法2：修改配置文件

编辑 `src/main/resources/application.yml`：

```yaml
security-checker:
  cve:
    nvd-api-key: "your-api-key-here"
```

### 方法3：命令行参数

```bash
java -jar maven-dependency-checker-1.0-SNAPSHOT.jar \
  --spring.config.additional-location=classpath:application.yml \
  --security-checker.cve.nvd-api-key="your-api-key-here" \
  --update
```

## 验证配置

运行更新命令时，如果看到以下消息说明API Key配置成功：

```
🔑 使用NVD API Key进行请求（更高限制）
```

如果看到以下消息说明未配置API Key：

```
⚠️  未配置NVD API Key，使用公共限制（建议申请API Key）
```

## 注意事项

1. **保护API Key**: 不要将API Key提交到版本控制系统
2. **使用环境变量**: 推荐使用环境变量方式配置，更安全
3. **遵守使用条款**: 请遵守NVD的API使用条款和限制
4. **监控使用量**: 避免超出API限制导致请求被拒绝

## 故障排除

### 403 Forbidden错误
- 检查API Key是否正确
- 确认API Key未过期
- 验证请求频率是否超限

### 429 Too Many Requests错误
- 降低请求频率
- 检查是否有其他程序同时使用相同API Key
- 等待一段时间后重试

### 连接超时
- 检查网络连接
- 确认防火墙设置
- 尝试增加超时时间
