# Maven依赖安全检查工具 - 配置文件示例
# 配置优先级: 命令行参数 > 环境变量 > 项目配置文件 > 用户配置文件 > 默认配置

# Spring Boot基础配置
spring:
  application:
    name: maven-dependency-checker
  
  # 数据库配置
  datasource:
    url: jdbc:h2:file:./database/security_checker;DB_CLOSE_ON_EXIT=FALSE
    driver-class-name: org.h2.Driver
    username: sa
    password: 
    
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    database-platform: org.hibernate.dialect.H2Dialect

# 安全检查器配置
security-checker:
  # CVE漏洞检查配置
  cve:
    # NVD API配置
    nvd-api-url: https://services.nvd.nist.gov/rest/json/cves/2.0
    
    # NVD API Key (强烈建议设置以获得更高的请求限制)
    # 获取方式: https://nvd.nist.gov/developers/request-an-api-key
    # 无API Key: 每30秒5个请求，有API Key: 每30秒50个请求
    nvd-api-key: ${NVD_API_KEY:}
    
    # 本地CVE数据库路径
    local-db-path: ./database/cve_data.db
    
    # CVE数据更新间隔（小时）
    update-interval-hours: 24
    
    # 启用模糊匹配（提高匹配准确性）
    enable-fuzzy-matching: true
    
    # API请求超时时间（秒）
    request-timeout-seconds: 30
    
    # 最大重试次数
    max-retries: 3
    
    # 批处理大小（影响内存使用和性能）
    batch-size: 1000

  # 许可证合规性检查配置
  license:
    # 限制性许可证列表（商业不友好的许可证）
    restricted-licenses:
      # GPL系列（强制开源）
      - GPL-2.0
      - GPL-3.0
      - AGPL-3.0
      - LGPL-2.1
      - LGPL-3.0
      
      # 其他限制性许可证
      - EUPL-1.2          # 欧盟公共许可证
      - OSL-3.0           # 开放软件许可证
      - SSPL-1.0          # 服务器端公共许可证
      - BUSL-1.1          # 商业源码许可证
      - Commons Clause    # 公共条款
      - CPAL-1.0          # 通用公共归属许可证
      - CDDL-1.0          # 通用开发和分发许可证
      - CDDL-1.1
      - EPL-1.0           # Eclipse公共许可证
      - EPL-2.0
      - MPL-2.0           # Mozilla公共许可证
    
    # SPDX许可证数据源URL
    spdx-license-url: https://raw.githubusercontent.com/spdx/license-list-data/master/json/licenses.json
    
    # 本地许可证数据库路径
    local-license-db: ./database/license_data.db
    
    # 启用许可证模糊匹配
    enable-fuzzy-matching: true
    
    # 严格模式（未知许可证视为风险）
    strict-mode: false

  # 报告生成配置
  report:
    # 报告输出目录
    output-dir: ./reports
    
    # 报告模板目录
    template-dir: classpath:/templates
    
    # 是否包含传递依赖
    include-transitive-deps: true
    
    # 最低严重级别阈值（LOW, MEDIUM, HIGH, CRITICAL）
    severity-threshold: MEDIUM
    
    # 启用详细报告
    enable-detailed-report: true
    
    # 包含修复建议
    include-recommendations: true

  # Maven配置
  maven:
    # Maven本地仓库路径
    local-repository: ~/.m2/repository
    
    # Maven中央仓库URL
    central-url: https://repo1.maven.org/maven2
    
    # 依赖解析超时时间（秒）
    timeout-seconds: 30
    
    # 离线模式（仅使用本地仓库）
    enable-offline-mode: false
    
    # 启用快照版本更新
    enable-snapshot-updates: false

# 日志配置
logging:
  level:
    com.security.checker: INFO
    org.springframework: WARN
    org.hibernate: WARN
  
  # 日志文件配置
  file:
    name: ./logs/maven-checker.log
    max-size: 10MB
    max-history: 30

# 环境变量配置示例
# 以下环境变量可以覆盖配置文件中的设置：
#
# NVD_API_KEY=your-nvd-api-key-here
# SECURITY_CHECKER_CVE_UPDATE_INTERVAL_HOURS=12
# SECURITY_CHECKER_REPORT_OUTPUT_DIR=/custom/reports/path
# SECURITY_CHECKER_MAVEN_LOCAL_REPOSITORY=/custom/m2/repository

# 使用说明：
# 1. 复制此文件到项目根目录或用户主目录下的 .maven-checker/ 目录
# 2. 根据需要修改配置参数
# 3. 设置环境变量 NVD_API_KEY 以获得更好的API访问限制
# 4. 运行工具时可以通过 --config 参数指定配置文件路径

# 配置优先级说明：
# 1. 命令行参数（最高优先级）
# 2. 环境变量
# 3. 项目根目录下的配置文件
# 4. 用户目录下的配置文件
# 5. 应用程序默认配置（最低优先级）

# 性能调优建议：
# - 如果项目依赖较多，可以增加 cve.batch-size 和 maven.timeout-seconds
# - 如果网络较慢，可以增加 cve.request-timeout-seconds 和 cve.max-retries
# - 如果磁盘空间有限，可以定期清理 database/ 目录下的数据文件
# - 如果不需要传递依赖检查，可以设置 report.include-transitive-deps: false

# 安全建议：
# - 定期更新CVE数据库（建议每天至少一次）
# - 根据项目需求调整 license.restricted-licenses 列表
# - 在CI/CD流水线中集成此工具进行自动化安全检查
# - 对于关键项目，建议设置 license.strict-mode: true
